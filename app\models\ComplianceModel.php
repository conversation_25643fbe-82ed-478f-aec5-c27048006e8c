<?php
class ComplianceModel {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all compliance frameworks
     *
     * @return array
     */
    public function getFrameworks() {
        $this->db->query('SELECT * FROM compliance_frameworks WHERE active = TRUE ORDER BY name ASC');
        return $this->db->resultSet();
    }

    /**
     * Get framework by ID
     *
     * @param int $id
     * @return object
     */
    public function getFrameworkById($id) {
        $this->db->query('SELECT * FROM compliance_frameworks WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get controls for a framework
     *
     * @param int $frameworkId
     * @return array
     */
    public function getControlsByFramework($frameworkId) {
        $this->db->query('SELECT * FROM compliance_controls WHERE framework_id = :framework_id AND active = TRUE ORDER BY control_id ASC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Get compliance status for an asset
     *
     * @param int $assetId
     * @param int $frameworkId
     * @return array
     */
    public function getAssetComplianceStatus($assetId, $frameworkId) {
        $this->db->query('SELECT ac.*, cc.control_id as control_identifier, cc.name as control_name,
                          u.name as assessed_by_name
                          FROM asset_compliance ac
                          JOIN compliance_controls cc ON ac.control_id = cc.id
                          LEFT JOIN users u ON ac.last_assessed_by = u.id
                          WHERE ac.asset_id = :asset_id AND cc.framework_id = :framework_id
                          ORDER BY cc.control_id ASC');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Update asset compliance status
     *
     * @param array $data
     * @return bool
     */
    public function updateAssetCompliance($data) {
        $this->db->query('INSERT INTO asset_compliance (asset_id, control_id, status, evidence, notes, last_assessed_by, last_assessed_date)
                          VALUES (:asset_id, :control_id, :status, :evidence, :notes, :last_assessed_by, NOW())
                          ON DUPLICATE KEY UPDATE
                          status = :status,
                          evidence = :evidence,
                          notes = :notes,
                          last_assessed_by = :last_assessed_by,
                          last_assessed_date = NOW()');

        $this->db->bind(':asset_id', $data['asset_id']);
        $this->db->bind(':control_id', $data['control_id']);
        $this->db->bind(':status', $data['status']);
        $this->db->bind(':evidence', $data['evidence']);
        $this->db->bind(':notes', $data['notes']);
        $this->db->bind(':last_assessed_by', $data['last_assessed_by']);

        return $this->db->execute();
    }

    /**
     * Get compliance summary for all assets
     *
     * @param int $frameworkId
     * @return array
     */
    public function getComplianceSummary($frameworkId) {
        $this->db->query('SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                          COUNT(DISTINCT cc.id) as total_controls,
                          SUM(CASE WHEN ac.status = "compliant" THEN 1 ELSE 0 END) as compliant_count,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          SUM(CASE WHEN ac.status = "not_applicable" THEN 1 ELSE 0 END) as not_applicable_count,
                          SUM(CASE WHEN ac.status = "in_progress" OR ac.status IS NULL THEN 1 ELSE 0 END) as in_progress_count
                          FROM assets a
                          CROSS JOIN compliance_controls cc
                          LEFT JOIN asset_compliance ac ON a.id = ac.asset_id AND cc.id = ac.control_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY a.id, a.computer_host_name, a.equipment_type, a.serial_number
                          ORDER BY compliant_count / total_controls DESC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Get compliance summary by control
     *
     * @param int $frameworkId
     * @return array
     */
    public function getComplianceSummaryByControl($frameworkId) {
        $this->db->query('SELECT cc.id, cc.control_id, cc.name,
                          COUNT(DISTINCT a.id) as total_assets,
                          SUM(CASE WHEN ac.status = "compliant" THEN 1 ELSE 0 END) as compliant_count,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          SUM(CASE WHEN ac.status = "not_applicable" THEN 1 ELSE 0 END) as not_applicable_count,
                          SUM(CASE WHEN ac.status = "in_progress" OR ac.status IS NULL THEN 1 ELSE 0 END) as in_progress_count
                          FROM compliance_controls cc
                          CROSS JOIN assets a
                          LEFT JOIN asset_compliance ac ON cc.id = ac.control_id AND a.id = ac.asset_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY cc.id, cc.control_id, cc.name
                          ORDER BY cc.control_id ASC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Generate compliance report
     *
     * @param int $frameworkId
     * @param int $userId
     * @return int Report ID
     */
    public function generateReport($frameworkId, $userId) {
        // Get framework details
        $framework = $this->getFrameworkById($frameworkId);

        // Get compliance summary by asset
        $assetSummary = $this->getComplianceSummary($frameworkId);

        // Get compliance summary by control
        $controlSummary = $this->getComplianceSummaryByControl($frameworkId);

        // Calculate overall compliance percentage
        $totalControls = 0;
        $compliantControls = 0;

        foreach($controlSummary as $control) {
            $totalControls += $control->total_assets;
            $compliantControls += $control->compliant_count;
        }

        $overallCompliance = $totalControls > 0 ? ($compliantControls / $totalControls) * 100 : 0;

        // Prepare report data
        $reportData = [
            'framework' => $framework,
            'generated_date' => date('Y-m-d H:i:s'),
            'overall_compliance' => $overallCompliance,
            'asset_summary' => $assetSummary,
            'control_summary' => $controlSummary
        ];

        // Save report
        $this->db->query('INSERT INTO compliance_reports (framework_id, report_name, report_date, report_data, generated_by)
                          VALUES (:framework_id, :report_name, CURDATE(), :report_data, :generated_by)');

        $this->db->bind(':framework_id', $frameworkId);
        $this->db->bind(':report_name', $framework->name . ' Compliance Report - ' . date('Y-m-d'));
        $this->db->bind(':report_data', json_encode($reportData));
        $this->db->bind(':generated_by', $userId);

        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    /**
     * Get report by ID
     *
     * @param int $reportId
     * @return object
     */
    public function getReportById($reportId) {
        $this->db->query('SELECT cr.*, cf.name as framework_name, u.name as generated_by_name
                          FROM compliance_reports cr
                          LEFT JOIN compliance_frameworks cf ON cr.framework_id = cf.id
                          LEFT JOIN users u ON cr.generated_by = u.id
                          WHERE cr.id = :id');
        $this->db->bind(':id', $reportId);
        $report = $this->db->single();

        if($report) {
            $report->report_data = json_decode($report->report_data);
        }

        return $report;
    }

    /**
     * Get recent reports
     *
     * @param int $limit
     * @return array
     */
    public function getRecentReports($limit = 5) {
        $this->db->query('SELECT cr.*, cf.name as framework_name, u.name as generated_by_name
                          FROM compliance_reports cr
                          LEFT JOIN compliance_frameworks cf ON cr.framework_id = cf.id
                          LEFT JOIN users u ON cr.generated_by = u.id
                          ORDER BY cr.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    /**
     * Get non-compliant assets
     *
     * @param int $frameworkId
     * @param int $limit
     * @return array
     */
    public function getNonCompliantAssets($frameworkId, $limit = 10) {
        $this->db->query('SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                          COUNT(DISTINCT cc.id) as total_controls,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          (SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) / COUNT(DISTINCT cc.id)) * 100 as non_compliant_percentage
                          FROM assets a
                          CROSS JOIN compliance_controls cc
                          LEFT JOIN asset_compliance ac ON a.id = ac.asset_id AND cc.id = ac.control_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY a.id, a.computer_host_name, a.equipment_type, a.serial_number
                          HAVING non_compliant_count > 0
                          ORDER BY non_compliant_percentage DESC
                          LIMIT :limit');
        $this->db->bind(':framework_id', $frameworkId);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    /**
     * Get control by ID
     *
     * @param int $controlId
     * @return object
     */
    public function getControlById($controlId) {
        $this->db->query('SELECT cc.*, cf.name as framework_name
                          FROM compliance_controls cc
                          JOIN compliance_frameworks cf ON cc.framework_id = cf.id
                          WHERE cc.id = :control_id');
        $this->db->bind(':control_id', $controlId);
        return $this->db->single();
    }

    /**
     * Get compliance status for an asset and control
     *
     * @param int $assetId
     * @param int $controlId
     * @return object
     */
    public function getAssetControlStatus($assetId, $controlId) {
        $this->db->query('SELECT * FROM asset_compliance
                          WHERE asset_id = :asset_id AND control_id = :control_id');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':control_id', $controlId);
        return $this->db->single();
    }
}
