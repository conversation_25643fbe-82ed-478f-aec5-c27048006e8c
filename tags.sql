-- Create tags table
CREATE TABLE IF NOT EXISTS tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    color VARCHAR(20) DEFAULT '#3b82f6', -- Default blue color
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create asset_tags junction table
CREATE TABLE IF NOT EXISTS asset_tags (
    asset_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (asset_id, tag_id),
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    <PERSON>OREI<PERSON><PERSON> KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- Add indexes for faster queries
CREATE INDEX idx_asset_tags_asset_id ON asset_tags(asset_id);
CREATE INDEX idx_asset_tags_tag_id ON asset_tags(tag_id);

-- Insert some default tags
INSERT INTO tags (name, color, created_by) VALUES 
('Important', '#ef4444'), -- Red
('New', '#10b981'), -- Green
('Maintenance', '#f59e0b'), -- Yellow/Orange
('Obsolete', '#6b7280'), -- Gray
('High Priority', '#8b5cf6'); -- Purple
