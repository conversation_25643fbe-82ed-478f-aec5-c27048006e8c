\doxysection{Database Class Reference}
\hypertarget{class_database}{}\label{class_database}\index{Database@{Database}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_database_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_database_a6b251c8058230359b2922377699c4f29}{query}} (\$sql)
\item 
\mbox{\hyperlink{class_database_ac8c79a80a9fd31d354865bbd0641cd6f}{bind}} (\$param, \$value, \$type=null)
\item 
\mbox{\hyperlink{class_database_a1909f4b7f8129c7790cb75de2ffbe1e4}{execute}} ()
\item 
\mbox{\hyperlink{class_database_a8d76b101d743baa11d67bed3d92e9ced}{result\+Set}} ()
\item 
\mbox{\hyperlink{class_database_a9024b1dea78a802643ae2c66aa1c4a24}{single}} ()
\item 
\mbox{\hyperlink{class_database_a82b073888555fc72e57142fe913db377}{row\+Count}} ()
\item 
\mbox{\hyperlink{class_database_a6384591ddfa09e979deb8a695bca0f67}{last\+Insert\+Id}} ()
\item 
\mbox{\hyperlink{class_database_aa80422c2209aa92dab0c767b2b6c7898}{paginated\+Result\+Set}} (\$page=1, \$per\+Page=10)
\item 
\mbox{\hyperlink{class_database_a4b44054e439ba58d94a43985eda4c41c}{get\+Total\+Count}} (\$count\+Query)
\item 
\mbox{\hyperlink{class_database_af708fa20ff0c04a5a9bd8badd63e632c}{get\+PDO}} ()
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
PDO \doxylink{class_database}{Database} Class Connect to database Create prepared statements Bind values Return rows and results 

\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_database_a095c5d389db211932136b53f25f39685}\index{Database@{Database}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_database_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_database_ac8c79a80a9fd31d354865bbd0641cd6f}\index{Database@{Database}!bind@{bind}}
\index{bind@{bind}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{bind()}{bind()}}
{\footnotesize\ttfamily \label{class_database_ac8c79a80a9fd31d354865bbd0641cd6f} 
bind (\begin{DoxyParamCaption}\item[{}]{\$param}{, }\item[{}]{\$value}{, }\item[{}]{\$type}{ = {\ttfamily null}}\end{DoxyParamCaption})}

\Hypertarget{class_database_a1909f4b7f8129c7790cb75de2ffbe1e4}\index{Database@{Database}!execute@{execute}}
\index{execute@{execute}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{execute()}{execute()}}
{\footnotesize\ttfamily \label{class_database_a1909f4b7f8129c7790cb75de2ffbe1e4} 
execute (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_af708fa20ff0c04a5a9bd8badd63e632c}\index{Database@{Database}!getPDO@{getPDO}}
\index{getPDO@{getPDO}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{getPDO()}{getPDO()}}
{\footnotesize\ttfamily \label{class_database_af708fa20ff0c04a5a9bd8badd63e632c} 
get\+PDO (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get the PDO instance for direct database operations

\begin{DoxyReturn}{Returns}
PDO\texorpdfstring{$\vert$}{|}null The PDO instance or null if not available 
\end{DoxyReturn}
\Hypertarget{class_database_a4b44054e439ba58d94a43985eda4c41c}\index{Database@{Database}!getTotalCount@{getTotalCount}}
\index{getTotalCount@{getTotalCount}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{getTotalCount()}{getTotalCount()}}
{\footnotesize\ttfamily \label{class_database_a4b44054e439ba58d94a43985eda4c41c} 
get\+Total\+Count (\begin{DoxyParamCaption}\item[{}]{\$count\+Query}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_a6384591ddfa09e979deb8a695bca0f67}\index{Database@{Database}!lastInsertId@{lastInsertId}}
\index{lastInsertId@{lastInsertId}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{lastInsertId()}{lastInsertId()}}
{\footnotesize\ttfamily \label{class_database_a6384591ddfa09e979deb8a695bca0f67} 
last\+Insert\+Id (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_aa80422c2209aa92dab0c767b2b6c7898}\index{Database@{Database}!paginatedResultSet@{paginatedResultSet}}
\index{paginatedResultSet@{paginatedResultSet}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{paginatedResultSet()}{paginatedResultSet()}}
{\footnotesize\ttfamily \label{class_database_aa80422c2209aa92dab0c767b2b6c7898} 
paginated\+Result\+Set (\begin{DoxyParamCaption}\item[{}]{\$page}{ = {\ttfamily 1}, }\item[{}]{\$per\+Page}{ = {\ttfamily 10}}\end{DoxyParamCaption})}

\Hypertarget{class_database_a6b251c8058230359b2922377699c4f29}\index{Database@{Database}!query@{query}}
\index{query@{query}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{query()}{query()}}
{\footnotesize\ttfamily \label{class_database_a6b251c8058230359b2922377699c4f29} 
query (\begin{DoxyParamCaption}\item[{}]{\$sql}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_a8d76b101d743baa11d67bed3d92e9ced}\index{Database@{Database}!resultSet@{resultSet}}
\index{resultSet@{resultSet}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{resultSet()}{resultSet()}}
{\footnotesize\ttfamily \label{class_database_a8d76b101d743baa11d67bed3d92e9ced} 
result\+Set (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_a82b073888555fc72e57142fe913db377}\index{Database@{Database}!rowCount@{rowCount}}
\index{rowCount@{rowCount}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{rowCount()}{rowCount()}}
{\footnotesize\ttfamily \label{class_database_a82b073888555fc72e57142fe913db377} 
row\+Count (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_database_a9024b1dea78a802643ae2c66aa1c4a24}\index{Database@{Database}!single@{single}}
\index{single@{single}!Database@{Database}}
\doxysubsubsection{\texorpdfstring{single()}{single()}}
{\footnotesize\ttfamily \label{class_database_a9024b1dea78a802643ae2c66aa1c4a24} 
single (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/core/\mbox{\hyperlink{_database_8php}{Database.\+php}}\end{DoxyCompactItemize}
