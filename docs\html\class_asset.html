<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Asset Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Asset Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60da5dbc75bf17137f158e70b09ef9ae" id="r_a60da5dbc75bf17137f158e70b09ef9ae"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a60da5dbc75bf17137f158e70b09ef9ae">getAssets</a> ($page=1, $perPage=10, $sortField='<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>', $sortOrder='DESC')</td></tr>
<tr class="separator:a60da5dbc75bf17137f158e70b09ef9ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69a1960e84310a44bf8dbdeda4e34fdb" id="r_a69a1960e84310a44bf8dbdeda4e34fdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a69a1960e84310a44bf8dbdeda4e34fdb">getAssetById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a69a1960e84310a44bf8dbdeda4e34fdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91dfab2328e0161d3dca63dfcb8b097f" id="r_a91dfab2328e0161d3dca63dfcb8b097f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a91dfab2328e0161d3dca63dfcb8b097f">addAsset</a> ($data)</td></tr>
<tr class="separator:a91dfab2328e0161d3dca63dfcb8b097f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acdfee112e7e094af03bb569595542f2f" id="r_acdfee112e7e094af03bb569595542f2f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acdfee112e7e094af03bb569595542f2f">updateAsset</a> ($data)</td></tr>
<tr class="separator:acdfee112e7e094af03bb569595542f2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae20ad8af6f2a4eda4e331bd8739f2dac" id="r_ae20ad8af6f2a4eda4e331bd8739f2dac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae20ad8af6f2a4eda4e331bd8739f2dac">deleteAsset</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:ae20ad8af6f2a4eda4e331bd8739f2dac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ed8debba2e7a08cce8fc517d61ef203" id="r_a9ed8debba2e7a08cce8fc517d61ef203"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9ed8debba2e7a08cce8fc517d61ef203">searchAssets</a> ($term, $page=1, $perPage=10, $sortField='<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>', $sortOrder='DESC')</td></tr>
<tr class="separator:a9ed8debba2e7a08cce8fc517d61ef203"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a488f0cf663d59902ee8008a71964f62c" id="r_a488f0cf663d59902ee8008a71964f62c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a488f0cf663d59902ee8008a71964f62c">advancedSearchAssets</a> ($params, $page=1, $perPage=10)</td></tr>
<tr class="separator:a488f0cf663d59902ee8008a71964f62c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaab70ee74a77249c0cb51a3ccd2eefd8" id="r_aaab70ee74a77249c0cb51a3ccd2eefd8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaab70ee74a77249c0cb51a3ccd2eefd8">getUniqueFieldValues</a> ($field)</td></tr>
<tr class="separator:aaab70ee74a77249c0cb51a3ccd2eefd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ab50787cfda440dabe402645ff8ac6d" id="r_a6ab50787cfda440dabe402645ff8ac6d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6ab50787cfda440dabe402645ff8ac6d">getAssetsForExport</a> ($term=null)</td></tr>
<tr class="separator:a6ab50787cfda440dabe402645ff8ac6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d272bd5a18c11c185325475ba86fe99" id="r_a7d272bd5a18c11c185325475ba86fe99"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7d272bd5a18c11c185325475ba86fe99">getAssetHistory</a> ($assetId)</td></tr>
<tr class="separator:a7d272bd5a18c11c185325475ba86fe99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f1964df346c70fa2a3ef319bd9acb7a" id="r_a4f1964df346c70fa2a3ef319bd9acb7a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4f1964df346c70fa2a3ef319bd9acb7a">getAssetCount</a> ()</td></tr>
<tr class="separator:a4f1964df346c70fa2a3ef319bd9acb7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42dc68dc181d645174c79dc64d32a78d" id="r_a42dc68dc181d645174c79dc64d32a78d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a42dc68dc181d645174c79dc64d32a78d">importFromCSV</a> ($file, $skipRows=7)</td></tr>
<tr class="separator:a42dc68dc181d645174c79dc64d32a78d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6425eeb4a115b8e037133d0bb3a8a82f" id="r_a6425eeb4a115b8e037133d0bb3a8a82f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6425eeb4a115b8e037133d0bb3a8a82f">getTotalAssetsCount</a> ()</td></tr>
<tr class="separator:a6425eeb4a115b8e037133d0bb3a8a82f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcb3b1e930889634a52b36d2652b2eca" id="r_adcb3b1e930889634a52b36d2652b2eca"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adcb3b1e930889634a52b36d2652b2eca">getAssetCountByType</a> ()</td></tr>
<tr class="separator:adcb3b1e930889634a52b36d2652b2eca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6d82658428d5a3485a9c14c00c4cb91" id="r_ab6d82658428d5a3485a9c14c00c4cb91"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab6d82658428d5a3485a9c14c00c4cb91">getAssetCountByAcquisitionDate</a> ()</td></tr>
<tr class="separator:ab6d82658428d5a3485a9c14c00c4cb91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acce8c1627f559a128e886639b0954511" id="r_acce8c1627f559a128e886639b0954511"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acce8c1627f559a128e886639b0954511">getAssetCountByEmployee</a> ()</td></tr>
<tr class="separator:acce8c1627f559a128e886639b0954511"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf321bfffefe63d041f00ad6adfca3fd" id="r_adf321bfffefe63d041f00ad6adfca3fd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adf321bfffefe63d041f00ad6adfca3fd">getAssetCountByOS</a> ()</td></tr>
<tr class="separator:adf321bfffefe63d041f00ad6adfca3fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdc448f418bdb34fd6cce81074ed02d8" id="r_abdc448f418bdb34fd6cce81074ed02d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abdc448f418bdb34fd6cce81074ed02d8">getAssetCountBySite</a> ()</td></tr>
<tr class="separator:abdc448f418bdb34fd6cce81074ed02d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77a2c945fd9edfec16d03c352b983b58" id="r_a77a2c945fd9edfec16d03c352b983b58"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77a2c945fd9edfec16d03c352b983b58">getAssetsByIds</a> ($ids)</td></tr>
<tr class="separator:a77a2c945fd9edfec16d03c352b983b58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab743e64ad3125a35028e7e8eb8c862fd" id="r_ab743e64ad3125a35028e7e8eb8c862fd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab743e64ad3125a35028e7e8eb8c862fd">serialNumberExists</a> ($serialNumber)</td></tr>
<tr class="separator:ab743e64ad3125a35028e7e8eb8c862fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a91dfab2328e0161d3dca63dfcb8b097f" name="a91dfab2328e0161d3dca63dfcb8b097f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91dfab2328e0161d3dca63dfcb8b097f">&#9670;&#160;</a></span>addAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a488f0cf663d59902ee8008a71964f62c" name="a488f0cf663d59902ee8008a71964f62c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a488f0cf663d59902ee8008a71964f62c">&#9670;&#160;</a></span>advancedSearchAssets()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">advancedSearchAssets </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$params</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$perPage</em></span><span class="paramdefsep"> = </span><span class="paramdefval">10</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae20ad8af6f2a4eda4e331bd8739f2dac" name="ae20ad8af6f2a4eda4e331bd8739f2dac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae20ad8af6f2a4eda4e331bd8739f2dac">&#9670;&#160;</a></span>deleteAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a69a1960e84310a44bf8dbdeda4e34fdb" name="a69a1960e84310a44bf8dbdeda4e34fdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69a1960e84310a44bf8dbdeda4e34fdb">&#9670;&#160;</a></span>getAssetById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4f1964df346c70fa2a3ef319bd9acb7a" name="a4f1964df346c70fa2a3ef319bd9acb7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f1964df346c70fa2a3ef319bd9acb7a">&#9670;&#160;</a></span>getAssetCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCount </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab6d82658428d5a3485a9c14c00c4cb91" name="ab6d82658428d5a3485a9c14c00c4cb91"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6d82658428d5a3485a9c14c00c4cb91">&#9670;&#160;</a></span>getAssetCountByAcquisitionDate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCountByAcquisitionDate </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acce8c1627f559a128e886639b0954511" name="acce8c1627f559a128e886639b0954511"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acce8c1627f559a128e886639b0954511">&#9670;&#160;</a></span>getAssetCountByEmployee()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCountByEmployee </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="adf321bfffefe63d041f00ad6adfca3fd" name="adf321bfffefe63d041f00ad6adfca3fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf321bfffefe63d041f00ad6adfca3fd">&#9670;&#160;</a></span>getAssetCountByOS()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCountByOS </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abdc448f418bdb34fd6cce81074ed02d8" name="abdc448f418bdb34fd6cce81074ed02d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abdc448f418bdb34fd6cce81074ed02d8">&#9670;&#160;</a></span>getAssetCountBySite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCountBySite </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="adcb3b1e930889634a52b36d2652b2eca" name="adcb3b1e930889634a52b36d2652b2eca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcb3b1e930889634a52b36d2652b2eca">&#9670;&#160;</a></span>getAssetCountByType()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCountByType </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7d272bd5a18c11c185325475ba86fe99" name="a7d272bd5a18c11c185325475ba86fe99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d272bd5a18c11c185325475ba86fe99">&#9670;&#160;</a></span>getAssetHistory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetHistory </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a60da5dbc75bf17137f158e70b09ef9ae" name="a60da5dbc75bf17137f158e70b09ef9ae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a60da5dbc75bf17137f158e70b09ef9ae">&#9670;&#160;</a></span>getAssets()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssets </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$perPage</em></span><span class="paramdefsep"> = </span><span class="paramdefval">10</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$sortField</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>'</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$sortOrder</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'DESC'</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a77a2c945fd9edfec16d03c352b983b58" name="a77a2c945fd9edfec16d03c352b983b58"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77a2c945fd9edfec16d03c352b983b58">&#9670;&#160;</a></span>getAssetsByIds()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetsByIds </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$ids</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6ab50787cfda440dabe402645ff8ac6d" name="a6ab50787cfda440dabe402645ff8ac6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ab50787cfda440dabe402645ff8ac6d">&#9670;&#160;</a></span>getAssetsForExport()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetsForExport </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$term</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6425eeb4a115b8e037133d0bb3a8a82f" name="a6425eeb4a115b8e037133d0bb3a8a82f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6425eeb4a115b8e037133d0bb3a8a82f">&#9670;&#160;</a></span>getTotalAssetsCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTotalAssetsCount </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaab70ee74a77249c0cb51a3ccd2eefd8" name="aaab70ee74a77249c0cb51a3ccd2eefd8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaab70ee74a77249c0cb51a3ccd2eefd8">&#9670;&#160;</a></span>getUniqueFieldValues()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUniqueFieldValues </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$field</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a42dc68dc181d645174c79dc64d32a78d" name="a42dc68dc181d645174c79dc64d32a78d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42dc68dc181d645174c79dc64d32a78d">&#9670;&#160;</a></span>importFromCSV()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">importFromCSV </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$file</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$skipRows</em></span><span class="paramdefsep"> = </span><span class="paramdefval">7</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9ed8debba2e7a08cce8fc517d61ef203" name="a9ed8debba2e7a08cce8fc517d61ef203"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ed8debba2e7a08cce8fc517d61ef203">&#9670;&#160;</a></span>searchAssets()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">searchAssets </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$term</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$perPage</em></span><span class="paramdefsep"> = </span><span class="paramdefval">10</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$sortField</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>'</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$sortOrder</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'DESC'</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab743e64ad3125a35028e7e8eb8c862fd" name="ab743e64ad3125a35028e7e8eb8c862fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab743e64ad3125a35028e7e8eb8c862fd">&#9670;&#160;</a></span>serialNumberExists()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">serialNumberExists </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$serialNumber</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if an asset with the given serial number already exists</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$serialNumber</td><td>The serial number to check </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the serial number exists, false otherwise </dd></dl>

</div>
</div>
<a id="acdfee112e7e094af03bb569595542f2f" name="acdfee112e7e094af03bb569595542f2f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acdfee112e7e094af03bb569595542f2f">&#9670;&#160;</a></span>updateAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="models_2_asset_8php.html">Asset.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
