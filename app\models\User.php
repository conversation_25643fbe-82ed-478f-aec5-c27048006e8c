<?php
class User {
    private $db;
    private $roleModel;

    public function __construct() {
        $this->db = new Database;
        $this->roleModel = new Role();
    }

    // Register user
    public function register($data) {
        $this->db->query('INSERT INTO users (name, email, password, role, status) VALUES(:name, :email, :password, :role, :status)');
        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $data['password']);
        $this->db->bind(':role', isset($data['role']) ? $data['role'] : 'user');
        $this->db->bind(':status', isset($data['status']) ? $data['status'] : 'inactive');

        // Execute
        if($this->db->execute()) {
            // The trigger will automatically add the user to the appropriate role
            return true;
        } else {
            return false;
        }
    }

    // Login User
    public function login($email, $password) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);

        $row = $this->db->single();

        // Check if user exists
        if(!$row) {
            return false;
        }

        // Check if user is active
        if($row->status == 'inactive') {
            return 'inactive';
        }

        // Check if account is locked
        if(SecurityEnhancements::isAccountLocked($row->id)) {
            return 'locked';
        }

        $hashed_password = $row->password;
        if(password_verify($password, $hashed_password)) {
            // Reset failed login attempts on successful login
            SecurityEnhancements::resetFailedLoginAttempts($row->id);

            // Log successful login
            SecurityEnhancements::logSecurityEvent('login', 'User logged in successfully', $row->id);
            SecurityEnhancements::recordLoginAttempt($email, true, $row->id);

            return $row;
        } else {
            // Increment failed login attempts
            SecurityEnhancements::incrementFailedLoginAttempts($row->id);

            // Log failed login
            SecurityEnhancements::logSecurityEvent('failed_login', 'Failed login attempt', $row->id);
            SecurityEnhancements::recordLoginAttempt($email, false, $row->id);

            return false;
        }
    }

    // Find user by email
    public function findUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        // Bind value
        $this->db->bind(':email', $email);

        $this->db->single();

        // Check row
        if($this->db->rowCount() > 0) {
            return true;
        } else {
            return false;
        }
    }

    // Get User by ID
    public function getUserById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        // Bind value
        $this->db->bind(':id', $id);

        $row = $this->db->single();

        return $row;
    }

    // Get all users
    public function getAllUsers() {
        $this->db->query('SELECT id, name, email, role, status, created_at FROM users ORDER BY name ASC');
        return $this->db->resultSet();
    }

    // Update user status
    public function updateStatus($id, $status) {
        $this->db->query('UPDATE users SET status = :status WHERE id = :id');
        // Bind values
        $this->db->bind(':id', $id);
        $this->db->bind(':status', $status);

        // Execute
        return $this->db->execute();
    }

    // Update user role (legacy method, now updates both users table and user_roles)
    public function updateRole($id, $role) {
        // Start a transaction
        $this->db->query('START TRANSACTION');
        $this->db->execute();

        try {
            // Update the role in the users table (for backward compatibility)
            $this->db->query('UPDATE users SET role = :role WHERE id = :id');
            $this->db->bind(':id', $id);
            $this->db->bind(':role', $role);
            $this->db->execute();

            // Get the corresponding role ID
            $roleId = null;
            if ($role == 'admin') {
                $roleObj = $this->roleModel->getRoleByName('Administrator');
                $roleId = $roleObj->id;
            } else {
                $roleObj = $this->roleModel->getRoleByName('User');
                $roleId = $roleObj->id;
            }

            // Delete existing roles
            $this->db->query('DELETE FROM user_roles WHERE user_id = :user_id');
            $this->db->bind(':user_id', $id);
            $this->db->execute();

            // Add the new role
            $this->db->query('INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)');
            $this->db->bind(':user_id', $id);
            $this->db->bind(':role_id', $roleId);
            $this->db->execute();

            // Commit the transaction
            $this->db->query('COMMIT');
            $this->db->execute();

            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->query('ROLLBACK');
            $this->db->execute();

            error_log('Error updating user role: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update the legacy role field in the users table
     * This is used for backward compatibility with the old role system
     *
     * @param int $id User ID
     * @param string $role Role value ('admin' or 'user')
     * @return bool True if successful, false otherwise
     */
    public function updateLegacyRole($id, $role) {
        $this->db->query('UPDATE users SET role = :role WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':role', $role);

        return $this->db->execute();
    }

    // Update user password
    public function updatePassword($id, $password) {
        $this->db->query('UPDATE users SET password = :password WHERE id = :id');
        // Bind values
        $this->db->bind(':id', $id);
        $this->db->bind(':password', $password);

        // Execute
        return $this->db->execute();
    }

    // Update user
    public function updateUser($data) {
        // Start with updating the name
        $query = 'UPDATE users SET name = :name';

        // Add password to query if provided
        if(isset($data['password'])) {
            $query .= ', password = :password';
        }

        $query .= ' WHERE id = :id';

        $this->db->query($query);

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);

        if(isset($data['password'])) {
            $this->db->bind(':password', $data['password']);
        }

        // Execute
        return $this->db->execute();
    }

    // Get user by email (returns user object)
    public function getUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        // Bind value
        $this->db->bind(':email', $email);

        $row = $this->db->single();

        return $row;
    }

    // Create password reset token
    public function createPasswordResetToken($email) {
        // Generate a random token
        $token = bin2hex(random_bytes(32));

        // Set expiration time (1 hour from now)
        $expires = date('Y-m-d H:i:s', time() + 3600);

        // Delete any existing tokens for this email
        $this->db->query('DELETE FROM password_reset_tokens WHERE email = :email');
        $this->db->bind(':email', $email);
        $this->db->execute();

        // Insert new token
        $this->db->query('INSERT INTO password_reset_tokens (email, token, expires_at) VALUES(:email, :token, :expires_at)');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);
        $this->db->bind(':expires_at', $expires);

        if($this->db->execute()) {
            return $token;
        } else {
            return false;
        }
    }

    // Verify password reset token
    public function verifyPasswordResetToken($email, $token) {
        $this->db->query('SELECT * FROM password_reset_tokens WHERE email = :email AND token = :token AND expires_at > NOW() AND used = 0');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);

        $this->db->single();

        if($this->db->rowCount() > 0) {
            return true;
        } else {
            return false;
        }
    }

    // Mark token as used
    public function markTokenAsUsed($email, $token) {
        $this->db->query('UPDATE password_reset_tokens SET used = 1 WHERE email = :email AND token = :token');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);

        return $this->db->execute();
    }

    // Reset password by email
    public function resetPasswordByEmail($email, $password) {
        $this->db->query('UPDATE users SET password = :password WHERE email = :email');
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $password);

        return $this->db->execute();
    }

    /**
     * Create a remember me token for a user
     *
     * @param int $userId User ID
     * @return array|bool Token data if created successfully, false otherwise
     */
    public function createRememberMeToken($userId) {
        return SecurityEnhancements::createRememberMeToken($userId);
    }

    /**
     * Verify a remember me token
     *
     * @param string $selector Token selector
     * @param string $validator Token validator
     * @return int|bool User ID if token is valid, false otherwise
     */
    public function verifyRememberMeToken($selector, $validator) {
        return SecurityEnhancements::verifyRememberMeToken($selector, $validator);
    }

    /**
     * Delete a remember me token
     *
     * @param string $selector Token selector
     * @return bool True if deleted successfully, false otherwise
     */
    public function deleteRememberMeToken($selector) {
        return SecurityEnhancements::deleteRememberMeToken($selector);
    }

    /**
     * Delete all remember me tokens for a user
     *
     * @param int $userId User ID
     * @return bool True if deleted successfully, false otherwise
     */
    public function deleteAllRememberMeTokens($userId) {
        return SecurityEnhancements::deleteAllRememberMeTokens($userId);
    }

    /**
     * Get all roles for a user
     *
     * @param int $userId User ID
     * @return array Array of role objects
     */
    public function getUserRoles($userId) {
        $this->db->query('SELECT r.* FROM roles r
                         JOIN user_roles ur ON r.id = ur.role_id
                         WHERE ur.user_id = :user_id
                         ORDER BY r.name ASC');
        $this->db->bind(':user_id', $userId);

        return $this->db->resultSet();
    }

    /**
     * Get primary role for a user (for backward compatibility)
     *
     * @param int $userId User ID
     * @return object|bool Role object or false if no roles
     */
    public function getPrimaryRole($userId) {
        $roles = $this->getUserRoles($userId);

        if (count($roles) > 0) {
            // Return the first role (assumed to be primary)
            return $roles[0];
        }

        return false;
    }

    /**
     * Check if a user has a specific role
     *
     * @param int $userId User ID
     * @param int|string $role Role ID or name
     * @return bool True if the user has the role, false otherwise
     */
    public function hasRole($userId, $role) {
        if (is_numeric($role)) {
            // Role ID provided
            $this->db->query('SELECT COUNT(*) as count FROM user_roles
                             WHERE user_id = :user_id AND role_id = :role_id');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_id', $role);
        } else {
            // Role name provided
            $this->db->query('SELECT COUNT(*) as count FROM user_roles ur
                             JOIN roles r ON ur.role_id = r.id
                             WHERE ur.user_id = :user_id AND r.name = :role_name');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_name', $role);
        }

        $result = $this->db->single();
        return $result->count > 0;
    }

    /**
     * Get all permissions for a user
     *
     * @param int $userId User ID
     * @return array Array of permission objects
     */
    public function getUserPermissions($userId) {
        $this->db->query('SELECT DISTINCT p.* FROM permissions p
                         JOIN role_permissions rp ON p.id = rp.permission_id
                         JOIN user_roles ur ON rp.role_id = ur.role_id
                         WHERE ur.user_id = :user_id
                         ORDER BY p.category, p.name');
        $this->db->bind(':user_id', $userId);

        return $this->db->resultSet();
    }

    /**
     * Check if a user has a specific permission
     *
     * @param int $userId User ID
     * @param int|string $permission Permission ID or name
     * @return bool True if the user has the permission, false otherwise
     */
    public function hasPermission($userId, $permission) {
        if (is_numeric($permission)) {
            // Permission ID provided
            $this->db->query('SELECT COUNT(*) as count FROM permissions p
                             JOIN role_permissions rp ON p.id = rp.permission_id
                             JOIN user_roles ur ON rp.role_id = ur.role_id
                             WHERE ur.user_id = :user_id AND p.id = :permission_id');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission_id', $permission);
        } else {
            // Permission name provided
            $this->db->query('SELECT COUNT(*) as count FROM permissions p
                             JOIN role_permissions rp ON p.id = rp.permission_id
                             JOIN user_roles ur ON rp.role_id = ur.role_id
                             WHERE ur.user_id = :user_id AND p.name = :permission_name');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission_name', $permission);
        }

        $result = $this->db->single();
        return $result->count > 0;
    }

    /**
     * Assign roles to a user
     *
     * @param int $userId User ID
     * @param array $roleIds Array of role IDs
     * @return bool True if successful, false otherwise
     */
    public function assignRoles($userId, $roleIds) {
        // First, remove all existing roles for this user
        $this->db->query('DELETE FROM user_roles WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        $this->db->execute();

        // Then, add the new roles
        $success = true;
        foreach ($roleIds as $roleId) {
            $this->db->query('INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_id', $roleId);

            if (!$this->db->execute()) {
                $success = false;
            }
        }

        return $success;
    }
}
