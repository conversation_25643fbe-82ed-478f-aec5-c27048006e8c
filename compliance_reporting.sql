-- Add compliance_frameworks table to define compliance frameworks
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add compliance_controls table to define controls within frameworks
CREATE TABLE IF NOT EXISTS compliance_controls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    framework_id INT NOT NULL,
    control_id VARCHAR(50) NOT NULL, -- e.g., "AC-1", "CM-7"
    name VARCHAR(255) NOT NULL,
    description TEXT,
    implementation_guidance TEXT,
    verification_procedure TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add asset_compliance table to track compliance status for assets
CREATE TABLE IF NOT EXISTS asset_compliance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    control_id INT NOT NULL,
    status ENUM('compliant', 'non_compliant', 'not_applicable', 'in_progress') DEFAULT 'in_progress',
    evidence TEXT,
    notes TEXT,
    last_assessed_by INT,
    last_assessed_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (control_id) REFERENCES compliance_controls(id) ON DELETE CASCADE,
    FOREIGN KEY (last_assessed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add compliance_reports table to store generated reports
CREATE TABLE IF NOT EXISTS compliance_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    framework_id INT,
    report_name VARCHAR(255) NOT NULL,
    report_date DATE NOT NULL,
    report_data LONGTEXT, -- JSON data
    generated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id) ON DELETE SET NULL,
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_compliance_controls_framework_id ON compliance_controls(framework_id);
CREATE INDEX idx_asset_compliance_asset_id ON asset_compliance(asset_id);
CREATE INDEX idx_asset_compliance_control_id ON asset_compliance(control_id);
CREATE INDEX idx_asset_compliance_status ON asset_compliance(status);
CREATE INDEX idx_compliance_reports_framework_id ON compliance_reports(framework_id);

-- Insert sample compliance frameworks
INSERT INTO compliance_frameworks (name, description, version, active) VALUES
('NIST SP 800-53', 'Security and Privacy Controls for Federal Information Systems and Organizations', 'Rev. 5', TRUE),
('ISO 27001', 'Information Security Management System', '2013', TRUE),
('CIS Controls', 'Center for Internet Security Controls', 'v8', TRUE);

-- Insert sample controls for NIST SP 800-53
INSERT INTO compliance_controls (framework_id, control_id, name, description, implementation_guidance, verification_procedure, active) VALUES
(1, 'CM-8', 'Information System Component Inventory', 'Develop and document an inventory of information system components that accurately reflects the current system.', 'Maintain an up-to-date inventory of all hardware assets within the organization.', 'Verify that all assets are recorded in the inventory system with accurate information.', TRUE),
(1, 'CM-2', 'Baseline Configuration', 'Develop, document, and maintain a current baseline configuration of the information system.', 'Document standard configurations for all endpoint types.', 'Verify that assets adhere to the documented baseline configurations.', TRUE),
(1, 'SI-3', 'Malicious Code Protection', 'Implement malicious code protection mechanisms at information system entry and exit points.', 'Ensure all endpoints have antivirus/EDR solutions installed and updated.', 'Verify that all assets have the required security software installed and updated.', TRUE);
