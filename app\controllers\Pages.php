<?php
class Pages extends Controller {
    public function __construct() {

    }

    public function index() {
        // Get some statistics for the home page
        $assetModel = $this->model('Asset');
        $assetCount = 0;

        if(isLoggedIn()) {
            // Get asset count if user is logged in
            $assetCount = $assetModel->getAssetCount();
        }

        $data = [
            'title' => 'Endpoint Visibility and Insight System',
            'description' => 'Comprehensive asset management system for tracking and managing ICT assets',
            'asset_count' => $assetCount
        ];

        $this->view('pages/index', $data);
    }

    public function about() {
        $data = [
            'title' => 'About Us',
            'description' => 'App to manage ICT assets inventory'
        ];

        $this->view('pages/about', $data);
    }
}
