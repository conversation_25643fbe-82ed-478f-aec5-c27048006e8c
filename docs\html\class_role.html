<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Role Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Role Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9d41efb9af8f1d2f8d9575069976da0" id="r_ad9d41efb9af8f1d2f8d9575069976da0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad9d41efb9af8f1d2f8d9575069976da0">getAllRoles</a> ()</td></tr>
<tr class="separator:ad9d41efb9af8f1d2f8d9575069976da0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbabecd774e69faa53d68bb8ddedbd54" id="r_adbabecd774e69faa53d68bb8ddedbd54"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adbabecd774e69faa53d68bb8ddedbd54">getRoleById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:adbabecd774e69faa53d68bb8ddedbd54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8314906463666f969d42371a864a164e" id="r_a8314906463666f969d42371a864a164e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8314906463666f969d42371a864a164e">getRoleByName</a> ($name)</td></tr>
<tr class="separator:a8314906463666f969d42371a864a164e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a768411d287ea069df4e0331e6bcefe09" id="r_a768411d287ea069df4e0331e6bcefe09"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a768411d287ea069df4e0331e6bcefe09">createRole</a> ($data)</td></tr>
<tr class="separator:a768411d287ea069df4e0331e6bcefe09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5aee45741b09c9eee6771d09a088c220" id="r_a5aee45741b09c9eee6771d09a088c220"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5aee45741b09c9eee6771d09a088c220">updateRole</a> ($data)</td></tr>
<tr class="separator:a5aee45741b09c9eee6771d09a088c220"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ef8d531b5037715f076c716eec66f66" id="r_a4ef8d531b5037715f076c716eec66f66"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4ef8d531b5037715f076c716eec66f66">deleteRole</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a4ef8d531b5037715f076c716eec66f66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbd22d5de18650bca342da1774129b78" id="r_acbd22d5de18650bca342da1774129b78"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acbd22d5de18650bca342da1774129b78">getRolePermissions</a> ($roleId)</td></tr>
<tr class="separator:acbd22d5de18650bca342da1774129b78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af180b7a33eaf553419c164116e7bcdb2" id="r_af180b7a33eaf553419c164116e7bcdb2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af180b7a33eaf553419c164116e7bcdb2">assignPermissions</a> ($roleId, $permissionIds)</td></tr>
<tr class="separator:af180b7a33eaf553419c164116e7bcdb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace6175dc80da953ba86a1f9f30d61ac4" id="r_ace6175dc80da953ba86a1f9f30d61ac4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ace6175dc80da953ba86a1f9f30d61ac4">hasPermission</a> ($roleId, $permissionId)</td></tr>
<tr class="separator:ace6175dc80da953ba86a1f9f30d61ac4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5bd1e5322a775264f3a66451069f600" id="r_ae5bd1e5322a775264f3a66451069f600"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae5bd1e5322a775264f3a66451069f600">getUsersWithRole</a> ($roleId)</td></tr>
<tr class="separator:ae5bd1e5322a775264f3a66451069f600"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a933ad1bc0d674f271c3f54b8ec236537" id="r_a933ad1bc0d674f271c3f54b8ec236537"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a933ad1bc0d674f271c3f54b8ec236537">updateRoleLastModified</a> ($roleId)</td></tr>
<tr class="separator:a933ad1bc0d674f271c3f54b8ec236537"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_role.html">Role</a> Model Handles database operations for roles </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="af180b7a33eaf553419c164116e7bcdb2" name="af180b7a33eaf553419c164116e7bcdb2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af180b7a33eaf553419c164116e7bcdb2">&#9670;&#160;</a></span>assignPermissions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">assignPermissions </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$permissionIds</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Assign permissions to a role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$roleId</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
    <tr><td class="paramtype">array</td><td class="paramname">$permissionIds</td><td>Array of permission IDs </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a768411d287ea069df4e0331e6bcefe09" name="a768411d287ea069df4e0331e6bcefe09"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a768411d287ea069df4e0331e6bcefe09">&#9670;&#160;</a></span>createRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">createRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_role.html">Role</a> data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a4ef8d531b5037715f076c716eec66f66" name="a4ef8d531b5037715f076c716eec66f66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ef8d531b5037715f076c716eec66f66">&#9670;&#160;</a></span>deleteRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="ad9d41efb9af8f1d2f8d9575069976da0" name="ad9d41efb9af8f1d2f8d9575069976da0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9d41efb9af8f1d2f8d9575069976da0">&#9670;&#160;</a></span>getAllRoles()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllRoles </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all roles</p>
<dl class="section return"><dt>Returns</dt><dd>array Array of role objects </dd></dl>

</div>
</div>
<a id="adbabecd774e69faa53d68bb8ddedbd54" name="adbabecd774e69faa53d68bb8ddedbd54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbabecd774e69faa53d68bb8ddedbd54">&#9670;&#160;</a></span>getRoleById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getRoleById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get role by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object <a class="el" href="class_role.html">Role</a> object </dd></dl>

</div>
</div>
<a id="a8314906463666f969d42371a864a164e" name="a8314906463666f969d42371a864a164e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8314906463666f969d42371a864a164e">&#9670;&#160;</a></span>getRoleByName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getRoleByName </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$name</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get role by name</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$name</td><td><a class="el" href="class_role.html">Role</a> name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object <a class="el" href="class_role.html">Role</a> object </dd></dl>

</div>
</div>
<a id="acbd22d5de18650bca342da1774129b78" name="acbd22d5de18650bca342da1774129b78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acbd22d5de18650bca342da1774129b78">&#9670;&#160;</a></span>getRolePermissions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getRolePermissions </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all permissions for a role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$roleId</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of permission objects </dd></dl>

</div>
</div>
<a id="ae5bd1e5322a775264f3a66451069f600" name="ae5bd1e5322a775264f3a66451069f600"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5bd1e5322a775264f3a66451069f600">&#9670;&#160;</a></span>getUsersWithRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUsersWithRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all users with a specific role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$roleId</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of user objects </dd></dl>

</div>
</div>
<a id="ace6175dc80da953ba86a1f9f30d61ac4" name="ace6175dc80da953ba86a1f9f30d61ac4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace6175dc80da953ba86a1f9f30d61ac4">&#9670;&#160;</a></span>hasPermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">hasPermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$permissionId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if a role has a specific permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$roleId</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$permissionId</td><td><a class="el" href="class_permission.html">Permission</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the role has the permission, false otherwise </dd></dl>

</div>
</div>
<a id="a5aee45741b09c9eee6771d09a088c220" name="a5aee45741b09c9eee6771d09a088c220"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5aee45741b09c9eee6771d09a088c220">&#9670;&#160;</a></span>updateRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update a role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_role.html">Role</a> data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a933ad1bc0d674f271c3f54b8ec236537" name="a933ad1bc0d674f271c3f54b8ec236537"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a933ad1bc0d674f271c3f54b8ec236537">&#9670;&#160;</a></span>updateRoleLastModified()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateRoleLastModified </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update the last_modified timestamp for a role This is used to track when permissions for a role were last updated</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$roleId</td><td><a class="el" href="class_role.html">Role</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_role_8php.html">Role.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
