-- Add asset_costs table to track all costs associated with assets
CREATE TABLE IF NOT EXISTS asset_costs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    cost_type ENUM('acquisition', 'maintenance', 'upgrade', 'repair', 'license', 'other') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    date_incurred DATE NOT NULL,
    fiscal_year INT,
    budget_category VARCHAR(100),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add budget_forecasts table to store budget projections
CREATE TABLE IF NOT EXISTS budget_forecasts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fiscal_year INT NOT NULL,
    quarter TINYINT,
    category VARCHAR(100) NOT NULL,
    projected_amount DECIMAL(10,2) NOT NULL,
    actual_amount DECIMAL(10,2),
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add depreciation_settings table to configure depreciation calculations
CREATE TABLE IF NOT EXISTS depreciation_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_type VARCHAR(100) NOT NULL,
    useful_life_years INT NOT NULL,
    depreciation_method ENUM('straight_line', 'declining_balance', 'sum_of_years_digits') DEFAULT 'straight_line',
    salvage_value_percentage DECIMAL(5,2) DEFAULT 10.00,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (equipment_type),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_asset_costs_asset_id ON asset_costs(asset_id);
CREATE INDEX idx_asset_costs_date_incurred ON asset_costs(date_incurred);
CREATE INDEX idx_asset_costs_fiscal_year ON asset_costs(fiscal_year);
CREATE INDEX idx_budget_forecasts_fiscal_year ON budget_forecasts(fiscal_year);
CREATE INDEX idx_budget_forecasts_category ON budget_forecasts(category);
