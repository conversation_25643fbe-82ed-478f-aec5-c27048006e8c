<?php
/**
 * Base Controller
 * Loads the models and views
 */
class Controller {
    // Load model
    public function model($model) {
        // Require model file
        require_once '../app/models/' . $model . '.php';

        // Instantiate model
        return new $model();
    }

    // Load view
    public function view($view, $data = []) {
        // Add CSRF token to data array for forms
        if (!isset($data['csrf_token'])) {
            $data['csrf_token'] = Security::generateCsrfToken();
        }

        // Check for view file
        if(file_exists('../app/views/' . $view . '.php')) {
            require_once '../app/views/' . $view . '.php';
        } else {
            // View does not exist
            die('View does not exist');
        }
    }

    /**
     * Sanitize POST data to prevent XSS attacks
     *
     * @param array $data The POST data to sanitize
     * @return array The sanitized data
     */
    protected function sanitizePostData($data) {
        return Security::sanitizeInput($data);
    }

    /**
     * Validate CSRF token
     *
     * @param string $token The token to validate
     * @return bool True if the token is valid, false otherwise
     */
    protected function validateCsrfToken($token) {
        // Debug log
        error_log("validate<PERSON>rfToken called with token: " . substr($token, 0, 10) . "...");

        if (!Security::validateCsrfToken($token)) {
            // Invalid CSRF token
            flash('error', 'Invalid security token. Please try again.', 'alert alert-danger');

            // Get the current controller and method from the URL
            $url = isset($_GET['url']) ? $_GET['url'] : '';
            $urlParts = explode('/', $url);
            $controller = isset($urlParts[0]) ? $urlParts[0] : '';
            $method = isset($urlParts[1]) ? $urlParts[1] : '';

            error_log("CSRF validation failed. URL: " . $url . ", Controller: " . $controller . ", Method: " . $method);

            // If we're in the import method, redirect back to the import page
            if ($controller == 'assets' && $method == 'import') {
                error_log("Redirecting back to import page");
                redirect('assets/import');
            } else {
                // Otherwise redirect to the controller's index page
                error_log("Redirecting to controller index page: " . $controller);
                redirect($controller);
            }
            return false;
        }
        return true;
    }
}
