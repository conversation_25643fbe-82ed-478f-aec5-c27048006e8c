<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Controller Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Controller Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Controller:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_controller.png" usemap="#Controller_map" alt=""/>
  <map id="Controller_map" name="Controller_map">
<area href="class_assets.html" alt="Assets" shape="rect" coords="95,56,180,80"/>
<area href="class_compliance.html" alt="Compliance" shape="rect" coords="95,112,180,136"/>
<area href="class_dashboard.html" alt="Dashboard" shape="rect" coords="95,168,180,192"/>
<area href="class_error_logs.html" alt="ErrorLogs" shape="rect" coords="95,224,180,248"/>
<area href="class_finance.html" alt="Finance" shape="rect" coords="95,280,180,304"/>
<area href="class_maintenance.html" alt="Maintenance" shape="rect" coords="95,336,180,360"/>
<area href="class_pages.html" alt="Pages" shape="rect" coords="95,392,180,416"/>
<area href="class_permissions.html" alt="Permissions" shape="rect" coords="95,448,180,472"/>
<area href="class_roles.html" alt="Roles" shape="rect" coords="95,504,180,528"/>
<area href="class_tags.html" alt="Tags" shape="rect" coords="95,560,180,584"/>
<area href="class_users.html" alt="Users" shape="rect" coords="95,616,180,640"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac531eb761b130b1925a8bae5c33af2fc" id="r_ac531eb761b130b1925a8bae5c33af2fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac531eb761b130b1925a8bae5c33af2fc">model</a> ($model)</td></tr>
<tr class="separator:ac531eb761b130b1925a8bae5c33af2fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11f0e20b30b899d00b009a9bb1afe43d" id="r_a11f0e20b30b899d00b009a9bb1afe43d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11f0e20b30b899d00b009a9bb1afe43d">view</a> ($view, $data=[])</td></tr>
<tr class="separator:a11f0e20b30b899d00b009a9bb1afe43d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pro-methods" name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a0d92de8136cebc006a407442aab9db0a" id="r_a0d92de8136cebc006a407442aab9db0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0d92de8136cebc006a407442aab9db0a">sanitizePostData</a> ($data)</td></tr>
<tr class="separator:a0d92de8136cebc006a407442aab9db0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf7b7d5aa2f9ec7a1f79646322121f52" id="r_aaf7b7d5aa2f9ec7a1f79646322121f52"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaf7b7d5aa2f9ec7a1f79646322121f52">validateCsrfToken</a> ($token)</td></tr>
<tr class="separator:aaf7b7d5aa2f9ec7a1f79646322121f52"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Base <a class="el" href="class_controller.html">Controller</a> Loads the models and views </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac531eb761b130b1925a8bae5c33af2fc" name="ac531eb761b130b1925a8bae5c33af2fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac531eb761b130b1925a8bae5c33af2fc">&#9670;&#160;</a></span>model()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">model </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$model</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0d92de8136cebc006a407442aab9db0a" name="a0d92de8136cebc006a407442aab9db0a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d92de8136cebc006a407442aab9db0a">&#9670;&#160;</a></span>sanitizePostData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">sanitizePostData </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel protected">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sanitize POST data to prevent XSS attacks</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td>The POST data to sanitize </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array The sanitized data </dd></dl>

</div>
</div>
<a id="aaf7b7d5aa2f9ec7a1f79646322121f52" name="aaf7b7d5aa2f9ec7a1f79646322121f52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf7b7d5aa2f9ec7a1f79646322121f52">&#9670;&#160;</a></span>validateCsrfToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">validateCsrfToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$token</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel protected">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Validate CSRF token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$token</td><td>The token to validate </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the token is valid, false otherwise </dd></dl>

</div>
</div>
<a id="a11f0e20b30b899d00b009a9bb1afe43d" name="a11f0e20b30b899d00b009a9bb1afe43d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11f0e20b30b899d00b009a9bb1afe43d">&#9670;&#160;</a></span>view()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">view </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$view</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span><span class="paramdefsep"> = </span><span class="paramdefval">[]</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/core/<a class="el" href="_controller_8php.html">Controller.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
