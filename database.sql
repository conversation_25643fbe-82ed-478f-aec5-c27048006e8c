-- Create database
CREATE DATABASE IF NOT EXISTS asset_visibility;
USE asset_visibility;

-- Create assets table
CREATE TABLE IF NOT EXISTS assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_date DATE,
    site_name VARCHA<PERSON>(100),
    employee_name VA<PERSON><PERSON><PERSON>(100),
    active_directory_name VA<PERSON><PERSON><PERSON>(100),
    position VARCHA<PERSON>(100),
    program_section VARCHAR(100),
    computer_host_name VA<PERSON>HA<PERSON>(100),
    equipment_type VARCHAR(50),
    acquisition_type VARCHAR(50),
    operating_system VARCHAR(50),
    administration_type VARCHAR(50),
    xdr_installed ENUM('Yes', 'No'),
    device_custodian VARCHAR(100),
    remarks TEXT,
    par_number VA<PERSON><PERSON><PERSON>(50),
    serial_number VARCHAR(50),
    acquisition_date DATE,
    estimated_useful_life DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    email VARCHAR(100) <PERSON>IQUE,
    password VARCHAR(255),
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user
INSERT INTO users (name, email, password, role) VALUES 
('Admin', '<EMAIL>', '$2y$10$HfzIhGCCaxqyaIdGgjARSuOKAcm1Uy82YfLuNaajn6JrjLWy9Sj/W', 'admin');
-- Default password: password123
