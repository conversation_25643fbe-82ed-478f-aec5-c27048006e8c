<?php
/**
 * Database Configuration
 */
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'asset_visibility');

/**
 * Application Configuration
 */
define('APPROOT', dirname(dirname(__FILE__)));
define('URLROOT', 'http://localhost/asset_visibility');
define('SITENAME', 'EVIS');

/**
 * Security Configuration
 */
// Google reCAPTCHA v2 keys (replace with your actual keys when deploying)
define('RECAPTCHA_SITE_KEY', '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'); // Test key
define('RECAPTCHA_SECRET_KEY', '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe'); // Test key

// Cookie settings for Remember Me functionality
define('COOKIE_SECURE', false); // Set to true in production with HTTPS
define('COOKIE_HTTP_ONLY', true);
define('COOKIE_SAME_SITE', 'Lax'); // Options: Strict, Lax, None

define('ASSET_LIFETIME', 1825); // Default 5 years (5*365) expected lifetime of an asset