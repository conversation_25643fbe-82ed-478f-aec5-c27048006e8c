<?php
class Assets extends Controller {
    private $assetModel;

    public function __construct() {
        $this->assetModel = $this->model('Asset');

        // Initialize CSRF token if it doesn't exist
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
    }

    public function index() {
        // Get page and per page parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
        $sortField = isset($_GET['sort']) ? $_GET['sort'] : 'id';
        $sortOrder = isset($_GET['order']) ? $_GET['order'] : 'DESC';

        // Validate page and per page
        if ($page < 1) $page = 1;
        if ($perPage < 1 || $perPage > 100) $perPage = 10;

        // Only allow specific per page values
        if (!in_array($perPage, [10, 25, 50, 100])) {
            $perPage = 10;
        }

        // Get assets with pagination and sorting
        $assetData = $this->assetModel->getAssets($page, $perPage, $sortField, $sortOrder);

        // Load tag model to get all available tags for the advanced search modal
        $tagModel = $this->model('Tag');
        $tags = $tagModel->getTags();

        // Get filter options for dropdowns in the advanced search modal
        $filterOptions = [
            'site_names' => $this->assetModel->getUniqueFieldValues('site_name'),
            'equipment_types' => $this->assetModel->getUniqueFieldValues('equipment_type'),
            'operating_systems' => $this->assetModel->getUniqueFieldValues('operating_system'),
            'administration_types' => $this->assetModel->getUniqueFieldValues('administration_type'),
            'xdr_options' => $this->assetModel->getUniqueFieldValues('xdr_installed'),
            'program_sections' => $this->assetModel->getUniqueFieldValues('program_section'),
            'acquisition_types' => $this->assetModel->getUniqueFieldValues('acquisition_type')
        ];

        $data = [
            'assets' => $assetData['assets'],
            'pagination' => [
                'total' => $assetData['total'],
                'page' => $assetData['page'],
                'perPage' => $assetData['perPage'],
                'lastPage' => $assetData['lastPage']
            ],
            'tags' => $tags,
            'filter_options' => $filterOptions,
            'csrf_token' => isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : '',
            'sort' => [
                'field' => $assetData['sortField'],
                'order' => $assetData['sortOrder']
            ]
        ];

        $this->view('assets/index', $data);
    }

    public function add() {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!$this->validateCsrfToken($_POST['csrf_token'] ?? '')) {
                return;
            }

            // Sanitize POST array
            $_POST = $this->sanitizePostData($_POST);

            $data = [
                'inventory_date' => trim($_POST['inventory_date']),
                'site_name' => trim($_POST['site_name']),
                'employee_name' => trim($_POST['employee_name']),
                'active_directory_name' => trim($_POST['active_directory_name']),
                'position' => trim($_POST['position']),
                'program_section' => trim($_POST['program_section']),
                'computer_host_name' => trim($_POST['computer_host_name']),
                'equipment_type' => trim($_POST['equipment_type']),
                'acquisition_type' => trim($_POST['acquisition_type']),
                'operating_system' => trim($_POST['operating_system']),
                'administration_type' => trim($_POST['administration_type']),
                'xdr_installed' => trim($_POST['xdr_installed']),
                'device_custodian' => trim($_POST['device_custodian']),
                'remarks' => trim($_POST['remarks']),
                'par_number' => trim($_POST['par_number']),
                'serial_number' => trim($_POST['serial_number']),
                'acquisition_date' => trim($_POST['acquisition_date']),
                'estimated_useful_life' => trim($_POST['estimated_useful_life']),
                'tags' => isset($_POST['tags']) ? $_POST['tags'] : [],
                'inventory_date_err' => '',
                'site_name_err' => '',
                'employee_name_err' => '',
                'computer_host_name_err' => '',
                'equipment_type_err' => '',
                'serial_number_err' => ''
            ];

            // Validate data
            if(empty($data['inventory_date'])) {
                $data['inventory_date_err'] = 'Please enter inventory date';
            }
            if(empty($data['site_name'])) {
                $data['site_name_err'] = 'Please enter site name';
            }
            if(empty($data['employee_name'])) {
                $data['employee_name_err'] = 'Please enter employee name';
            }
            if(empty($data['computer_host_name'])) {
                $data['computer_host_name_err'] = 'Please enter computer/host name';
            }
            if(empty($data['equipment_type'])) {
                $data['equipment_type_err'] = 'Please enter equipment type';
            }
            if(empty($data['serial_number'])) {
                $data['serial_number_err'] = 'Please enter serial number';
            }

            // Make sure no errors
            if(empty($data['inventory_date_err']) && empty($data['site_name_err']) && empty($data['employee_name_err']) && empty($data['computer_host_name_err']) && empty($data['equipment_type_err']) && empty($data['serial_number_err'])) {
                // Validated
                if($this->assetModel->addAsset($data)) {
                    // Redirect to assets page
                    header('Location: ' . URLROOT . '/assets');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('assets/add', $data);
            }
        } else {
            // Load tag model to get all available tags
            $tagModel = $this->model('Tag');
            $availableTags = $tagModel->getTags();

            $data = [
                'inventory_date' => '',
                'site_name' => '',
                'employee_name' => '',
                'active_directory_name' => '',
                'position' => '',
                'program_section' => '',
                'computer_host_name' => '',
                'equipment_type' => '',
                'acquisition_type' => '',
                'operating_system' => '',
                'administration_type' => '',
                'xdr_installed' => '',
                'device_custodian' => '',
                'remarks' => '',
                'par_number' => '',
                'serial_number' => '',
                'acquisition_date' => '',
                'estimated_useful_life' => '',
                'tags' => [],
                'available_tags' => $availableTags,
                'inventory_date_err' => '',
                'site_name_err' => '',
                'employee_name_err' => '',
                'computer_host_name_err' => '',
                'equipment_type_err' => '',
                'serial_number_err' => ''
            ];

            $this->view('assets/add', $data);
        }
    }

    public function edit($id) {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!$this->validateCsrfToken($_POST['csrf_token'] ?? '')) {
                return;
            }

            // Sanitize POST array
            $_POST = $this->sanitizePostData($_POST);

            $data = [
                'id' => $id,
                'inventory_date' => trim($_POST['inventory_date']),
                'site_name' => trim($_POST['site_name']),
                'employee_name' => trim($_POST['employee_name']),
                'active_directory_name' => trim($_POST['active_directory_name']),
                'position' => trim($_POST['position']),
                'program_section' => trim($_POST['program_section']),
                'computer_host_name' => trim($_POST['computer_host_name']),
                'equipment_type' => trim($_POST['equipment_type']),
                'acquisition_type' => trim($_POST['acquisition_type']),
                'operating_system' => trim($_POST['operating_system']),
                'administration_type' => trim($_POST['administration_type']),
                'xdr_installed' => trim($_POST['xdr_installed']),
                'device_custodian' => trim($_POST['device_custodian']),
                'remarks' => trim($_POST['remarks']),
                'par_number' => trim($_POST['par_number']),
                'serial_number' => trim($_POST['serial_number']),
                'acquisition_date' => trim($_POST['acquisition_date']),
                'estimated_useful_life' => trim($_POST['estimated_useful_life']),
                'tags' => isset($_POST['tags']) ? $_POST['tags'] : [],
                'inventory_date_err' => '',
                'site_name_err' => '',
                'employee_name_err' => '',
                'computer_host_name_err' => '',
                'equipment_type_err' => '',
                'serial_number_err' => ''
            ];

            // Validate data
            if(empty($data['inventory_date'])) {
                $data['inventory_date_err'] = 'Please enter inventory date';
            }
            if(empty($data['site_name'])) {
                $data['site_name_err'] = 'Please enter site name';
            }
            if(empty($data['employee_name'])) {
                $data['employee_name_err'] = 'Please enter employee name';
            }
            if(empty($data['computer_host_name'])) {
                $data['computer_host_name_err'] = 'Please enter computer/host name';
            }
            if(empty($data['equipment_type'])) {
                $data['equipment_type_err'] = 'Please enter equipment type';
            }
            if(empty($data['serial_number'])) {
                $data['serial_number_err'] = 'Please enter serial number';
            }

            // Make sure no errors
            if(empty($data['inventory_date_err']) && empty($data['site_name_err']) && empty($data['employee_name_err']) && empty($data['computer_host_name_err']) && empty($data['equipment_type_err']) && empty($data['serial_number_err'])) {
                // Validated
                if($this->assetModel->updateAsset($data)) {
                    // Redirect to assets page
                    header('Location: ' . URLROOT . '/assets');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('assets/edit', $data);
            }
        } else {
            // Get existing asset from model
            $asset = $this->assetModel->getAssetById($id);

            // Load tag model to get tags for this asset and all available tags
            $tagModel = $this->model('Tag');
            $assetTags = $tagModel->getTagsForAsset($id);
            $availableTags = $tagModel->getTags();

            $data = [
                'id' => $id,
                'inventory_date' => $asset->inventory_date,
                'site_name' => $asset->site_name,
                'employee_name' => $asset->employee_name,
                'active_directory_name' => $asset->active_directory_name,
                'position' => $asset->position,
                'program_section' => $asset->program_section,
                'computer_host_name' => $asset->computer_host_name,
                'equipment_type' => $asset->equipment_type,
                'acquisition_type' => $asset->acquisition_type,
                'operating_system' => $asset->operating_system,
                'administration_type' => $asset->administration_type,
                'xdr_installed' => $asset->xdr_installed,
                'device_custodian' => $asset->device_custodian,
                'remarks' => $asset->remarks,
                'par_number' => $asset->par_number,
                'serial_number' => $asset->serial_number,
                'acquisition_date' => $asset->acquisition_date,
                'estimated_useful_life' => $asset->estimated_useful_life,
                'inventory_date_err' => '',
                'site_name_err' => '',
                'employee_name_err' => '',
                'computer_host_name_err' => '',
                'equipment_type_err' => '',
                'serial_number_err' => '',
                'asset_tags' => $assetTags,
                'available_tags' => $availableTags
            ];

            $this->view('assets/edit', $data);
        }
    }

    public function show($id) {
        $asset = $this->assetModel->getAssetById($id);
        $assetHistory = $this->assetModel->getAssetHistory($id);

        // Load tag model to get tags for this asset
        $tagModel = $this->model('Tag');
        $tags = $tagModel->getTagsForAsset($id);

        $data = [
            'asset' => $asset,
            'history' => $assetHistory,
            'tags' => $tags
        ];

        $this->view('assets/show', $data);
    }

    public function delete($id) {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!$this->validateCsrfToken($_POST['csrf_token'] ?? '')) {
                return;
            }

            // Sanitize and validate ID
            $id = filter_var($id, FILTER_VALIDATE_INT);
            if($id === false) {
                flash('asset_message', 'Invalid asset ID', 'alert alert-danger');
                redirect('assets');
                return;
            }

            if($this->assetModel->deleteAsset($id)) {
                // Redirect to assets page
                flash('asset_message', 'Asset deleted successfully', 'alert alert-success');
                redirect('assets');
            } else {
                flash('asset_message', 'Something went wrong', 'alert alert-danger');
                redirect('assets');
            }
        } else {
            redirect('assets');
        }
    }

    public function search() {
        // Get page and per page parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;

        // Validate page and per page
        if ($page < 1) $page = 1;
        if ($perPage < 1 || $perPage > 100) $perPage = 10;

        // Only allow specific per page values
        if (!in_array($perPage, [10, 25, 50, 100])) {
            $perPage = 10;
        }

        // Check if it's a basic or advanced search
        $isAdvancedSearch = isset($_GET['advanced']) && $_GET['advanced'] == '1';

        if($isAdvancedSearch) {
            // Process advanced search
            $searchParams = [];

            // Get all possible filter parameters
            $filterFields = [
                'term', 'site_name', 'employee_name', 'computer_host_name',
                'equipment_type', 'acquisition_type', 'operating_system',
                'administration_type', 'xdr_installed', 'device_custodian',
                'program_section', 'acquisition_date_from', 'acquisition_date_to',
                'inventory_date_from', 'inventory_date_to', 'sort_by', 'sort_order',
                'tag_id'
            ];

            // Build search parameters from GET variables
            foreach($filterFields as $field) {
                if(isset($_GET[$field]) && $_GET[$field] !== '') {
                    $searchParams[$field] = trim($_GET[$field]);
                }
            }

            // Get filter options for dropdowns
            $filterOptions = [
                'site_names' => $this->assetModel->getUniqueFieldValues('site_name'),
                'equipment_types' => $this->assetModel->getUniqueFieldValues('equipment_type'),
                'operating_systems' => $this->assetModel->getUniqueFieldValues('operating_system'),
                'administration_types' => $this->assetModel->getUniqueFieldValues('administration_type'),
                'xdr_options' => $this->assetModel->getUniqueFieldValues('xdr_installed'),
                'program_sections' => $this->assetModel->getUniqueFieldValues('program_section'),
                'acquisition_types' => $this->assetModel->getUniqueFieldValues('acquisition_type')
            ];

            // Get all tags for tag filter
            $tagModel = $this->model('Tag');
            $tags = $tagModel->getTags();

            // Perform the search with pagination
            $assetData = $this->assetModel->advancedSearchAssets($searchParams, $page, $perPage);

            $data = [
                'assets' => $assetData['assets'],
                'pagination' => [
                    'total' => $assetData['total'],
                    'page' => $assetData['page'],
                    'perPage' => $assetData['perPage'],
                    'lastPage' => $assetData['lastPage']
                ],
                'params' => $searchParams,
                'filter_options' => $filterOptions,
                'is_advanced' => true,
                'tags' => $tags
            ];

            $this->view('assets/search', $data);
        } else {
            // Process basic search or show all assets if no term provided
            $term = isset($_GET['term']) ? trim($_GET['term']) : '';
            $sortField = isset($_GET['sort']) ? $_GET['sort'] : 'id';
            $sortOrder = isset($_GET['order']) ? $_GET['order'] : 'DESC';

            // Get all assets if no search term is provided
            if (empty($term)) {
                $assetData = $this->assetModel->getAssets($page, $perPage, $sortField, $sortOrder);
            } else {
                $assetData = $this->assetModel->searchAssets($term, $page, $perPage, $sortField, $sortOrder);
            }

            // Get all tags for tag filter
            $tagModel = $this->model('Tag');
            $tags = $tagModel->getTags();

            // Get filter options for dropdowns in the advanced search modal
            $filterOptions = [
                'site_names' => $this->assetModel->getUniqueFieldValues('site_name'),
                'equipment_types' => $this->assetModel->getUniqueFieldValues('equipment_type'),
                'operating_systems' => $this->assetModel->getUniqueFieldValues('operating_system'),
                'administration_types' => $this->assetModel->getUniqueFieldValues('administration_type'),
                'xdr_options' => $this->assetModel->getUniqueFieldValues('xdr_installed'),
                'program_sections' => $this->assetModel->getUniqueFieldValues('program_section'),
                'acquisition_types' => $this->assetModel->getUniqueFieldValues('acquisition_type')
            ];

            $data = [
                'assets' => $assetData['assets'],
                'pagination' => [
                    'total' => $assetData['total'],
                    'page' => $assetData['page'],
                    'perPage' => $assetData['perPage'],
                    'lastPage' => $assetData['lastPage']
                ],
                'term' => $term,
                'is_advanced' => false,
                'tags' => $tags,
                'filter_options' => $filterOptions,
                'csrf_token' => isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : '',
                'sort' => [
                    'field' => $assetData['sortField'],
                    'order' => $assetData['sortOrder']
                ]
            ];

            $this->view('assets/search', $data);
        }
    }

    // Bulk edit selected assets
    public function bulkEdit() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Handle both POST and GET requests
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!$this->validateCsrfToken($_POST['csrf_token'] ?? '')) {
                return;
            }

            // Check if any assets were selected
            if(!isset($_POST['selected_assets']) || empty($_POST['selected_assets'])) {
                flash('asset_message', 'No assets selected for editing', 'alert alert-danger');
                redirect('assets');
                return;
            }

            // Sanitize and validate asset IDs
            $assetIds = [];
            foreach($_POST['selected_assets'] as $id) {
                $id = filter_var($id, FILTER_VALIDATE_INT);
                if($id !== false) {
                    $assetIds[] = $id;
                }
            }

            if(empty($assetIds)) {
                flash('asset_message', 'Invalid asset IDs', 'alert alert-danger');
                redirect('assets');
                return;
            }

            // Get the selected assets
            $assets = $this->assetModel->getAssetsByIds($assetIds);

            // Get filter options for dropdowns
            $filterOptions = [
                'site_names' => $this->assetModel->getUniqueFieldValues('site_name'),
                'equipment_types' => $this->assetModel->getUniqueFieldValues('equipment_type'),
                'operating_systems' => $this->assetModel->getUniqueFieldValues('operating_system'),
                'administration_types' => $this->assetModel->getUniqueFieldValues('administration_type'),
                'xdr_options' => $this->assetModel->getUniqueFieldValues('xdr_installed'),
                'program_sections' => $this->assetModel->getUniqueFieldValues('program_section'),
                'acquisition_types' => $this->assetModel->getUniqueFieldValues('acquisition_type')
            ];

            // Get all tags
            $tagModel = $this->model('Tag');
            $tags = $tagModel->getTags();

            $data = [
                'assets' => $assets,
                'filter_options' => $filterOptions,
                'tags' => $tags,
                'csrf_token' => $_SESSION['csrf_token']
            ];

            $this->view('assets/bulkEdit', $data);
        } else {
            // For GET requests, redirect to assets page
            redirect('assets');
        }
    }



    public function export($term = '', $format = '') {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user has permission to export assets
        if(!hasPermission('export_assets')) {
            flash('asset_message', 'You do not have permission to export assets', 'alert alert-danger');
            redirect('assets');
        }

        // Check if this is an advanced search export
        if(($term === 'advanced' || $format === 'advanced') && isset($_GET)) {
            // Process advanced search parameters
            $searchParams = [];

            // Get all possible filter parameters
            $filterFields = [
                'term', 'site_name', 'employee_name', 'computer_host_name',
                'equipment_type', 'acquisition_type', 'operating_system',
                'administration_type', 'xdr_installed', 'device_custodian',
                'program_section', 'acquisition_date_from', 'acquisition_date_to',
                'inventory_date_from', 'inventory_date_to', 'sort_by', 'sort_order',
                'tag_id'
            ];

            // Build search parameters from GET variables
            foreach($filterFields as $field) {
                if(isset($_GET[$field]) && $_GET[$field] !== '') {
                    $searchParams[$field] = trim($_GET[$field]);
                }
            }

            // Debug output - comment out in production
            // echo "<h2>Advanced Search Export</h2>";
            // echo "<pre>Search Params: " . print_r($searchParams, true) . "</pre>";

            // Get assets using advanced search without pagination
            $searchParams['page'] = 1;
            $searchParams['perPage'] = 1000000; // Set a very high number to get all results
            $assetData = $this->assetModel->advancedSearchAssets($searchParams);
            $assets = $assetData['assets'];

            // Debug output - comment out in production
            // echo "<pre>Assets Count: " . count($assets) . "</pre>";
            // if (count($assets) > 0) {
            //     echo "<pre>First Asset: " . print_r($assets[0], true) . "</pre>";
            // }
            // exit;

            // Set filename for advanced search
            $filename = "assets_advanced_search_" . date('Y-m-d') . ".csv";
        } else {
            // Basic search or all assets
            $searchTerm = !empty($term) ? urldecode($term) : null;

            // Get assets for export (all or filtered by search term)
            $assets = $this->assetModel->getAssetsForExport($searchTerm);

            // Set filename based on whether it's a search result or all assets
            $filename = $searchTerm
                ? "assets_search_" . preg_replace('/[^a-zA-Z0-9]/', '_', $searchTerm) . "_" . date('Y-m-d') . ".csv"
                : "assets_export_" . date('Y-m-d') . ".csv";
        }

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Create a file pointer connected to the output stream
        $output = fopen('php://output', 'w');

        // Add UTF-8 BOM to fix Excel display issues with special characters
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Set column headers
        fputcsv($output, [
            'Inventory Date',
            'Site Name',
            'Employee Name',
            'Active Directory Name',
            'Position',
            'Program Section',
            'Computer Host Name',
            'Equipment Type',
            'Acquisition Type',
            'Operating System',
            'Administration Type',
            'XDR Installed',
            'Device Custodian',
            'Remarks',
            'PAR Number',
            'Serial Number',
            'Acquisition Date',
            'Estimated Useful Life',
            'Tags'
        ]);

        // Load tag model to get tags for assets
        $tagModel = $this->model('Tag');

        // Output each row of data
        foreach ($assets as $asset) {
            // Get tags for this asset
            $assetTags = $tagModel->getTagsForAsset($asset->id);
            $tagNames = [];

            foreach ($assetTags as $tag) {
                $tagNames[] = $tag->name;
            }

            // Join tag names with commas
            $tagsString = implode(', ', $tagNames);

            fputcsv($output, [
                $asset->inventory_date,
                $asset->site_name,
                $asset->employee_name,
                $asset->active_directory_name,
                $asset->position,
                $asset->program_section,
                $asset->computer_host_name,
                $asset->equipment_type,
                $asset->acquisition_type,
                $asset->operating_system,
                $asset->administration_type,
                $asset->xdr_installed,
                $asset->device_custodian,
                $asset->remarks,
                $asset->par_number,
                $asset->serial_number,
                $asset->acquisition_date,
                $asset->estimated_useful_life,
                $tagsString
            ]);
        }

        // Close the file pointer
        fclose($output);
        exit;
    }

    public function test_upload() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user is admin
        if(!isAdmin()) {
            flash('asset_message', 'Only administrators can access this page', 'alert alert-danger');
            redirect('assets');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Debug log for file upload
            error_log("Test upload POST request received");
            error_log("FILES array: " . print_r($_FILES, true));

            if(isset($_FILES['test_file']) && $_FILES['test_file']['error'] == 0) {
                $fileName = $_FILES['test_file']['name'];
                $fileTmpPath = $_FILES['test_file']['tmp_name'];
                $fileSize = $_FILES['test_file']['size'];
                $fileType = $_FILES['test_file']['type'];

                error_log("File uploaded: " . $fileName . ", size: " . $fileSize . ", type: " . $fileType . ", tmp_path: " . $fileTmpPath);

                // Check if file exists
                if(file_exists($fileTmpPath)) {
                    error_log("File exists at tmp_path: " . $fileTmpPath);

                    // Try to read the file
                    $fileContent = file_get_contents($fileTmpPath);
                    if($fileContent !== false) {
                        error_log("Successfully read file content, length: " . strlen($fileContent));

                        // Display success message
                        $data = [
                            'success' => 'File uploaded successfully: ' . $fileName . ' (' . $fileSize . ' bytes)',
                            'file_info' => [
                                'name' => $fileName,
                                'size' => $fileSize,
                                'type' => $fileType,
                                'content_preview' => substr($fileContent, 0, 100) . '...'
                            ]
                        ];
                    } else {
                        error_log("Failed to read file content");
                        $data = [
                            'error' => 'Failed to read file content'
                        ];
                    }
                } else {
                    error_log("File does not exist at tmp_path: " . $fileTmpPath);
                    $data = [
                        'error' => 'File upload failed: File does not exist at temporary path'
                    ];
                }
            } else {
                $errorCode = isset($_FILES['test_file']) ? $_FILES['test_file']['error'] : 'No file uploaded';
                error_log("File upload error: " . $errorCode);
                $data = [
                    'error' => 'File upload error: ' . $this->getFileUploadErrorMessage($errorCode)
                ];
            }
        } else {
            $data = [];
        }

        $data['csrf_token'] = $_SESSION['csrf_token'];
        $this->view('assets/test_upload', $data);
    }

    private function getFileUploadErrorMessage($errorCode) {
        switch($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
            case UPLOAD_ERR_FORM_SIZE:
                return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
            case UPLOAD_ERR_PARTIAL:
                return 'The uploaded file was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing a temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'A PHP extension stopped the file upload';
            default:
                return 'Unknown upload error';
        }
    }

    public function import_results() {
        // Debug log
        error_log("import_results method called. Session ID: " . session_id());

        // Check if user is logged in
        if(!isLoggedIn()) {
            error_log("User not logged in, redirecting to login page");
            redirect('users/login');
        }

        // Check if user has permission to import assets
        if(!hasPermission('import_assets')) {
            error_log("User does not have permission to import assets, redirecting to assets page");
            flash('asset_message', 'You do not have permission to import assets', 'alert alert-danger');
            redirect('assets');
        }

        // Check if import results exist in session
        error_log("Checking for import_results in session: " . (isset($_SESSION['import_results']) ? 'Found' : 'Not found'));

        if(isset($_SESSION['import_results'])) {
            $data = [
                'results' => $_SESSION['import_results']
            ];

            error_log("Import results found in session: " . print_r($_SESSION['import_results'], true));

            // Clear the session data after retrieving it
            unset($_SESSION['import_results']);

            // Render the import_results view
            error_log("Rendering import_results view");
            $this->view('assets/import_results', $data);
        } else {
            // No import results found, redirect to import page
            error_log("No import results found in session, redirecting to import page");
            redirect('assets/import');
        }
    }

    public function import() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user has permission to import assets
        if(!hasPermission('import_assets')) {
            flash('asset_message', 'You do not have permission to import assets', 'alert alert-danger');
            redirect('assets');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!$this->validateCsrfToken($_POST['csrf_token'] ?? '')) {
                return;
            }

            // Validate reCAPTCHA
            if(!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
                $data = [
                    'error' => 'Please complete the CAPTCHA verification',
                    'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                    'csrf_token' => $_SESSION['csrf_token']
                ];
                $this->view('assets/import', $data);
                return;
            }

            // Verify reCAPTCHA with Google
            $recaptchaResponse = $_POST['g-recaptcha-response'];
            $url = 'https://www.google.com/recaptcha/api/siteverify';
            $data = [
                'secret' => RECAPTCHA_SECRET_KEY,
                'response' => $recaptchaResponse,
                'remoteip' => $_SERVER['REMOTE_ADDR']
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data)
                ]
            ];

            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            $captchaResult = json_decode($result);

            if(!$captchaResult->success) {
                // Log failed CAPTCHA attempt
                SecurityEnhancements::logSecurityEvent('failed_captcha', 'Failed CAPTCHA verification on import page', $_SESSION['user_id']);

                $data = [
                    'error' => 'CAPTCHA verification failed. Please try again.',
                    'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                    'csrf_token' => $_SESSION['csrf_token']
                ];
                $this->view('assets/import', $data);
                return;
            }

            // Check rate limiting
            if(!SecurityEnhancements::checkImportRateLimit($_SESSION['user_id'])) {
                // Log rate limit event
                SecurityEnhancements::logSecurityEvent('import_rate_limit', 'Import rate limit exceeded', $_SESSION['user_id']);

                $data = [
                    'error' => 'You have reached the maximum number of imports allowed per hour. Please try again later.',
                    'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                    'csrf_token' => $_SESSION['csrf_token']
                ];
                $this->view('assets/import', $data);
                return;
            }

            // Sanitize POST data
            $_POST = $this->sanitizePostData($_POST);

            // Debug log for file upload
            error_log("File upload check: " . print_r($_FILES, true));

            // Check if file was uploaded
            if(isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == 0) {
                $fileName = $_FILES['csv_file']['name'];
                $fileSize = $_FILES['csv_file']['size'];
                $fileTmpPath = $_FILES['csv_file']['tmp_name'];

                // Get file extension
                $file_ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

                // Validate file is a CSV
                if($file_ext != 'csv') {
                    // Log security event
                    SecurityEnhancements::logSecurityEvent('invalid_file_type', 'Invalid file type uploaded: ' . $file_ext, $_SESSION['user_id']);
                    SecurityEnhancements::recordImportOperation($_SESSION['user_id'], $fileName, $fileSize, false);

                    $data = [
                        'error' => 'Please upload a CSV file',
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                // Validate file size
                if(!SecurityEnhancements::validateFileSize($fileSize)) {
                    // Log security event
                    SecurityEnhancements::logSecurityEvent('file_size_exceeded', 'File size exceeded: ' . $fileSize . ' bytes', $_SESSION['user_id']);
                    SecurityEnhancements::recordImportOperation($_SESSION['user_id'], $fileName, $fileSize, false);

                    $data = [
                        'error' => 'File size exceeds the maximum allowed size of ' . (SecurityEnhancements::MAX_IMPORT_FILE_SIZE / 1048576) . 'MB',
                        'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                // Validate CSV content
                $contentValidation = SecurityEnhancements::validateCsvContent($fileTmpPath);
                if($contentValidation !== true) {
                    // Log security event
                    SecurityEnhancements::logSecurityEvent('invalid_csv_content', 'Invalid CSV content: ' . implode(', ', $contentValidation), $_SESSION['user_id']);
                    SecurityEnhancements::recordImportOperation($_SESSION['user_id'], $fileName, $fileSize, false);

                    $data = [
                        'error' => 'Invalid CSV content: ' . implode(', ', $contentValidation),
                        'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                // Get number of rows to skip
                $skip_rows = isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1;

                // Log import attempt
                SecurityEnhancements::logSecurityEvent('import_attempt', 'CSV import attempt: ' . $fileName, $_SESSION['user_id']);

                // Debug log before import
                error_log("About to import CSV file: " . $fileName . ", tmp_path: " . $fileTmpPath . ", skip_rows: " . $skip_rows);

                // Verify the file exists
                if (!file_exists($fileTmpPath)) {
                    error_log("File does not exist at path: " . $fileTmpPath);
                    $data = [
                        'error' => 'File upload failed. Please try again.',
                        'skip_rows' => $skip_rows,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                // Try to read the file content to verify it's accessible
                $fileContent = file_get_contents($fileTmpPath);
                if ($fileContent === false) {
                    error_log("Failed to read file content from: " . $fileTmpPath);
                    $data = [
                        'error' => 'Failed to read file content. Please try again.',
                        'skip_rows' => $skip_rows,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                error_log("Successfully read file content, length: " . strlen($fileContent));

                // Debug: Check file content preview
                $contentPreview = substr($fileContent, 0, 200);
                error_log("File content preview: " . $contentPreview);

                // Create a temporary copy of the file to ensure it doesn't get removed
                $tempDir = sys_get_temp_dir();
                $tempFile = $tempDir . '/' . uniqid('csv_import_') . '.csv';
                if (!copy($fileTmpPath, $tempFile)) {
                    error_log("Failed to create temporary copy of file at: " . $tempFile);
                    $data = [
                        'error' => 'Failed to process file. Please try again.',
                        'skip_rows' => $skip_rows,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                error_log("Created temporary copy of file at: " . $tempFile);

                // Verify the temporary file exists and is readable
                if (!file_exists($tempFile)) {
                    error_log("Temporary file does not exist at: " . $tempFile);
                    $data = [
                        'error' => 'Failed to create temporary file. Please try again.',
                        'skip_rows' => $skip_rows,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                // Check temporary file content
                $tempFileContent = file_get_contents($tempFile);
                if ($tempFileContent === false) {
                    error_log("Failed to read temporary file content from: " . $tempFile);
                    $data = [
                        'error' => 'Failed to read temporary file. Please try again.',
                        'skip_rows' => $skip_rows,
                        'csrf_token' => $_SESSION['csrf_token']
                    ];
                    $this->view('assets/import', $data);
                    return;
                }

                error_log("Successfully read temporary file content, length: " . strlen($tempFileContent));

                // Debug: Check if content matches
                if (strlen($fileContent) !== strlen($tempFileContent)) {
                    error_log("WARNING: Original file and temporary file have different lengths!");
                }

                // Import the CSV file using the temporary copy
                $_FILES['csv_file']['tmp_name'] = $tempFile;
                error_log("Passing file to importFromCSV: " . print_r($_FILES['csv_file'], true));
                $import_results = $this->assetModel->importFromCSV($_FILES['csv_file'], $skip_rows);

                // Debug log after import
                error_log("Import results: " . print_r($import_results, true));

                // Record import operation
                $success = ($import_results['success'] > 0);
                SecurityEnhancements::recordImportOperation($_SESSION['user_id'], $fileName, $fileSize, $success);

                // Log import result
                if($success) {
                    SecurityEnhancements::logSecurityEvent('import_success', 'Successfully imported ' . $import_results['success'] . ' assets', $_SESSION['user_id']);
                } else {
                    SecurityEnhancements::logSecurityEvent('import_failure', 'Failed to import assets: ' . implode(', ', $import_results['errors']), $_SESSION['user_id']);
                }

                // Set flash message
                if($import_results['success'] > 0) {
                    flash('asset_message', 'Successfully imported ' . $import_results['success'] . ' assets', 'alert alert-success');
                } else {
                    flash('asset_message', 'No assets were imported', 'alert alert-warning');
                }

                // Prepare data for the import_results view
                $data = [
                    'title' => 'Import Results',
                    'results' => $import_results
                ];

                // Debug log before rendering view
                error_log("Rendering import_results view with data: " . print_r($data, true));

                // Render the import_results view directly
                $this->view('assets/import_results', $data);
            } else {
                // Log file upload error
                $errorCode = $_FILES['csv_file']['error'] ?? 'No file uploaded';
                SecurityEnhancements::logSecurityEvent('file_upload_error', 'File upload error: ' . $errorCode, $_SESSION['user_id']);

                $data = [
                    'error' => 'Please select a file to upload',
                    'skip_rows' => isset($_POST['skip_rows']) ? (int)$_POST['skip_rows'] : 1,
                    'csrf_token' => $_SESSION['csrf_token']
                ];
                $this->view('assets/import', $data);
            }
        } else {
            $data = [
                'skip_rows' => 1,
                'csrf_token' => $_SESSION['csrf_token']
            ];

            $this->view('assets/import', $data);
        }
    }
}
