-- Role and Permission Management Tables

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON>AR<PERSON> (role_id, permission_id),
    FOR<PERSON><PERSON><PERSON>EY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- Create user_roles junction table (for future support of multiple roles per user)
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Insert default roles
INSERT INTO roles (name, description, is_system_role) VALUES 
('Administrator', 'Full system access with all permissions', TRUE),
('User', 'Standard user with limited permissions', TRUE);

-- Insert default permissions
INSERT INTO permissions (name, description, category) VALUES
-- User Management Permissions
('manage_users', 'Create, edit, and delete users', 'User Management'),
('view_users', 'View user list', 'User Management'),
('manage_roles', 'Create, edit, and delete roles', 'User Management'),
('assign_roles', 'Assign roles to users', 'User Management'),
('manage_permissions', 'Create, edit, and delete permissions', 'User Management'),
('assign_permissions', 'Assign permissions to roles', 'User Management'),

-- Asset Management Permissions
('create_assets', 'Create new assets', 'Asset Management'),
('edit_assets', 'Edit existing assets', 'Asset Management'),
('delete_assets', 'Delete assets', 'Asset Management'),
('view_assets', 'View asset list and details', 'Asset Management'),
('import_assets', 'Import assets from CSV', 'Asset Management'),
('export_assets', 'Export assets to CSV', 'Asset Management'),

-- Maintenance Management Permissions
('manage_maintenance', 'Create and edit maintenance records', 'Maintenance'),
('view_maintenance', 'View maintenance records', 'Maintenance'),
('manage_guidelines', 'Create and edit maintenance guidelines', 'Maintenance'),
('view_guidelines', 'View maintenance guidelines', 'Maintenance'),

-- Finance Permissions
('manage_finance', 'Access to financial features', 'Finance'),
('view_finance', 'View financial information', 'Finance'),
('manage_budget', 'Manage budget settings', 'Finance'),

-- System Permissions
('view_dashboard', 'Access to dashboard', 'System'),
('view_reports', 'Access to reports', 'System'),
('view_logs', 'Access to system logs', 'System'),
('manage_system', 'Manage system settings', 'System');

-- Assign all permissions to Administrator role
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'Administrator'),
    id
FROM permissions;

-- Assign basic permissions to User role
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'User'),
    id
FROM permissions
WHERE name IN ('view_assets', 'view_dashboard', 'view_maintenance', 'view_guidelines');

-- Migrate existing users to the new role system
INSERT INTO user_roles (user_id, role_id)
SELECT 
    u.id,
    CASE 
        WHEN u.role = 'admin' THEN (SELECT id FROM roles WHERE name = 'Administrator')
        ELSE (SELECT id FROM roles WHERE name = 'User')
    END
FROM users u;

-- Add a trigger to automatically update user_roles when a new user is created
DELIMITER //
CREATE TRIGGER after_user_insert
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    INSERT INTO user_roles (user_id, role_id)
    SELECT 
        NEW.id,
        CASE 
            WHEN NEW.role = 'admin' THEN (SELECT id FROM roles WHERE name = 'Administrator')
            ELSE (SELECT id FROM roles WHERE name = 'User')
        END;
END //
DELIMITER ;
