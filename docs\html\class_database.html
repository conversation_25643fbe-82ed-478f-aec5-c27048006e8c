<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Database Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Database Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b251c8058230359b2922377699c4f29" id="r_a6b251c8058230359b2922377699c4f29"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6b251c8058230359b2922377699c4f29">query</a> ($sql)</td></tr>
<tr class="separator:a6b251c8058230359b2922377699c4f29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8c79a80a9fd31d354865bbd0641cd6f" id="r_ac8c79a80a9fd31d354865bbd0641cd6f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac8c79a80a9fd31d354865bbd0641cd6f">bind</a> ($param, $value, $type=null)</td></tr>
<tr class="separator:ac8c79a80a9fd31d354865bbd0641cd6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1909f4b7f8129c7790cb75de2ffbe1e4" id="r_a1909f4b7f8129c7790cb75de2ffbe1e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1909f4b7f8129c7790cb75de2ffbe1e4">execute</a> ()</td></tr>
<tr class="separator:a1909f4b7f8129c7790cb75de2ffbe1e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d76b101d743baa11d67bed3d92e9ced" id="r_a8d76b101d743baa11d67bed3d92e9ced"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8d76b101d743baa11d67bed3d92e9ced">resultSet</a> ()</td></tr>
<tr class="separator:a8d76b101d743baa11d67bed3d92e9ced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9024b1dea78a802643ae2c66aa1c4a24" id="r_a9024b1dea78a802643ae2c66aa1c4a24"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9024b1dea78a802643ae2c66aa1c4a24">single</a> ()</td></tr>
<tr class="separator:a9024b1dea78a802643ae2c66aa1c4a24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82b073888555fc72e57142fe913db377" id="r_a82b073888555fc72e57142fe913db377"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82b073888555fc72e57142fe913db377">rowCount</a> ()</td></tr>
<tr class="separator:a82b073888555fc72e57142fe913db377"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6384591ddfa09e979deb8a695bca0f67" id="r_a6384591ddfa09e979deb8a695bca0f67"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6384591ddfa09e979deb8a695bca0f67">lastInsertId</a> ()</td></tr>
<tr class="separator:a6384591ddfa09e979deb8a695bca0f67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa80422c2209aa92dab0c767b2b6c7898" id="r_aa80422c2209aa92dab0c767b2b6c7898"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa80422c2209aa92dab0c767b2b6c7898">paginatedResultSet</a> ($page=1, $perPage=10)</td></tr>
<tr class="separator:aa80422c2209aa92dab0c767b2b6c7898"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b44054e439ba58d94a43985eda4c41c" id="r_a4b44054e439ba58d94a43985eda4c41c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4b44054e439ba58d94a43985eda4c41c">getTotalCount</a> ($countQuery)</td></tr>
<tr class="separator:a4b44054e439ba58d94a43985eda4c41c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af708fa20ff0c04a5a9bd8badd63e632c" id="r_af708fa20ff0c04a5a9bd8badd63e632c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af708fa20ff0c04a5a9bd8badd63e632c">getPDO</a> ()</td></tr>
<tr class="separator:af708fa20ff0c04a5a9bd8badd63e632c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>PDO <a class="el" href="class_database.html">Database</a> Class Connect to database Create prepared statements Bind values Return rows and results </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac8c79a80a9fd31d354865bbd0641cd6f" name="ac8c79a80a9fd31d354865bbd0641cd6f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8c79a80a9fd31d354865bbd0641cd6f">&#9670;&#160;</a></span>bind()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bind </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$param</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$type</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1909f4b7f8129c7790cb75de2ffbe1e4" name="a1909f4b7f8129c7790cb75de2ffbe1e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1909f4b7f8129c7790cb75de2ffbe1e4">&#9670;&#160;</a></span>execute()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">execute </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af708fa20ff0c04a5a9bd8badd63e632c" name="af708fa20ff0c04a5a9bd8badd63e632c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af708fa20ff0c04a5a9bd8badd63e632c">&#9670;&#160;</a></span>getPDO()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getPDO </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get the PDO instance for direct database operations</p>
<dl class="section return"><dt>Returns</dt><dd>PDO|null The PDO instance or null if not available </dd></dl>

</div>
</div>
<a id="a4b44054e439ba58d94a43985eda4c41c" name="a4b44054e439ba58d94a43985eda4c41c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4b44054e439ba58d94a43985eda4c41c">&#9670;&#160;</a></span>getTotalCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTotalCount </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$countQuery</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6384591ddfa09e979deb8a695bca0f67" name="a6384591ddfa09e979deb8a695bca0f67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6384591ddfa09e979deb8a695bca0f67">&#9670;&#160;</a></span>lastInsertId()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">lastInsertId </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa80422c2209aa92dab0c767b2b6c7898" name="aa80422c2209aa92dab0c767b2b6c7898"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa80422c2209aa92dab0c767b2b6c7898">&#9670;&#160;</a></span>paginatedResultSet()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">paginatedResultSet </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$perPage</em></span><span class="paramdefsep"> = </span><span class="paramdefval">10</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6b251c8058230359b2922377699c4f29" name="a6b251c8058230359b2922377699c4f29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b251c8058230359b2922377699c4f29">&#9670;&#160;</a></span>query()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">query </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$sql</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8d76b101d743baa11d67bed3d92e9ced" name="a8d76b101d743baa11d67bed3d92e9ced"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d76b101d743baa11d67bed3d92e9ced">&#9670;&#160;</a></span>resultSet()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">resultSet </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82b073888555fc72e57142fe913db377" name="a82b073888555fc72e57142fe913db377"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82b073888555fc72e57142fe913db377">&#9670;&#160;</a></span>rowCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">rowCount </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9024b1dea78a802643ae2c66aa1c4a24" name="a9024b1dea78a802643ae2c66aa1c4a24"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9024b1dea78a802643ae2c66aa1c4a24">&#9670;&#160;</a></span>single()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">single </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/core/<a class="el" href="_database_8php.html">Database.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
