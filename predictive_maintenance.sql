-- Add maintenance_history table to track all maintenance activities
CREATE TABLE IF NOT EXISTS maintenance_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    maintenance_type ENUM('preventive', 'corrective', 'upgrade', 'inspection') NOT NULL,
    description TEXT NOT NULL,
    performed_by INT,
    performed_date DATE NOT NULL,
    cost DECIMAL(10,2),
    next_scheduled_date DATE,
    status ENUM('scheduled', 'completed', 'cancelled', 'overdue') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add asset_health_metrics table to store calculated health scores
CREATE TABLE IF NOT EXISTS asset_health_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    health_score DECIMAL(5,2) NOT NULL, -- 0-100 score
    estimated_remaining_life INT, -- in days
    failure_probability DECIMAL(5,2), -- 0-100 percentage
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
);

-- Add asset_failures table to track failures for predictive modeling
CREATE TABLE IF NOT EXISTS asset_failures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    failure_date DATE NOT NULL,
    failure_type VARCHAR(100) NOT NULL,
    description TEXT,
    resolution TEXT,
    downtime_hours DECIMAL(10,2),
    repair_cost DECIMAL(10,2),
    reported_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_maintenance_history_asset_id ON maintenance_history(asset_id);
CREATE INDEX idx_maintenance_history_performed_date ON maintenance_history(performed_date);
CREATE INDEX idx_asset_health_metrics_asset_id ON asset_health_metrics(asset_id);
CREATE INDEX idx_asset_failures_asset_id ON asset_failures(asset_id);
CREATE INDEX idx_asset_failures_failure_date ON asset_failures(failure_date);
