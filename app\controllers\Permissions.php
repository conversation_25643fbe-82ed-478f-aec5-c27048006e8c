<?php
class Permissions extends Controller {
    private $permissionModel;
    private $roleModel;

    public function __construct() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user has permission to manage permissions
        if(!hasPermission('manage_permissions')) {
            flash('permission_message', 'You do not have permission to manage permissions', 'alert alert-danger');
            redirect('dashboard');
        }

        $this->permissionModel = $this->model('Permission');
        $this->roleModel = $this->model('Role');
    }

    /**
     * Display all permissions
     */
    public function index() {
        $permissions = $this->permissionModel->getAllPermissions();
        $categories = $this->permissionModel->getAllCategories();

        $data = [
            'permissions' => $permissions,
            'categories' => $categories
        ];

        $this->view('permissions/index', $data);
    }

    /**
     * Show permission details
     *
     * @param int $id Permission ID
     */
    public function show($id) {
        $permission = $this->permissionModel->getPermissionById($id);

        if(!$permission) {
            flash('permission_message', 'Permission not found', 'alert alert-danger');
            redirect('permissions');
        }

        $roles = $this->permissionModel->getRolesWithPermission($id);

        $data = [
            'permission' => $permission,
            'roles' => $roles
        ];

        $this->view('permissions/show', $data);
    }

    /**
     * Add a new permission
     */
    public function add() {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('permission_message', 'Security token validation failed', 'alert alert-danger');
                redirect('permissions');
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'category' => trim($_POST['category']),
                'name_err' => '',
                'description_err' => '',
                'category_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter permission name';
            } else {
                // Check if permission already exists
                $existingPermission = $this->permissionModel->getPermissionByName($data['name']);
                if($existingPermission) {
                    $data['name_err'] = 'Permission with this name already exists';
                }
            }

            // Validate description
            if(empty($data['description'])) {
                $data['description_err'] = 'Please enter permission description';
            }

            // Validate category
            if(empty($data['category'])) {
                $data['category_err'] = 'Please enter permission category';
            }

            // Make sure errors are empty
            if(empty($data['name_err']) && empty($data['description_err']) && empty($data['category_err'])) {
                // Create permission
                if($this->permissionModel->createPermission($data)) {
                    flash('permission_message', 'Permission added successfully', 'alert alert-success');
                    redirect('permissions');
                } else {
                    flash('permission_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('permissions/add', $data);
                }
            } else {
                // Load view with errors
                $this->view('permissions/add', $data);
            }
        } else {
            // Get all categories for dropdown
            $categories = $this->permissionModel->getAllCategories();

            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'category' => '',
                'categories' => $categories,
                'name_err' => '',
                'description_err' => '',
                'category_err' => ''
            ];

            $this->view('permissions/add', $data);
        }
    }

    /**
     * Edit a permission
     *
     * @param int $id Permission ID
     */
    public function edit($id) {
        $permission = $this->permissionModel->getPermissionById($id);

        if(!$permission) {
            flash('permission_message', 'Permission not found', 'alert alert-danger');
            redirect('permissions');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('permission_message', 'Security token validation failed', 'alert alert-danger');
                redirect('permissions');
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'category' => trim($_POST['category']),
                'name_err' => '',
                'description_err' => '',
                'category_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter permission name';
            } else {
                // Check if permission already exists (excluding current permission)
                $existingPermission = $this->permissionModel->getPermissionByName($data['name']);
                if($existingPermission && $existingPermission->id != $id) {
                    $data['name_err'] = 'Permission with this name already exists';
                }
            }

            // Validate description
            if(empty($data['description'])) {
                $data['description_err'] = 'Please enter permission description';
            }

            // Validate category
            if(empty($data['category'])) {
                $data['category_err'] = 'Please enter permission category';
            }

            // Make sure errors are empty
            if(empty($data['name_err']) && empty($data['description_err']) && empty($data['category_err'])) {
                // Update permission
                if($this->permissionModel->updatePermission($data)) {
                    flash('permission_message', 'Permission updated successfully', 'alert alert-success');
                    redirect('permissions');
                } else {
                    flash('permission_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('permissions/edit', $data);
                }
            } else {
                // Load view with errors
                $this->view('permissions/edit', $data);
            }
        } else {
            // Get all categories for dropdown
            $categories = $this->permissionModel->getAllCategories();

            // Init data
            $data = [
                'id' => $id,
                'name' => $permission->name,
                'description' => $permission->description,
                'category' => $permission->category,
                'categories' => $categories,
                'name_err' => '',
                'description_err' => '',
                'category_err' => ''
            ];

            $this->view('permissions/edit', $data);
        }
    }

    /**
     * Delete a permission
     *
     * @param int $id Permission ID
     */
    public function delete($id) {
        // Check if it's a POST request
        if($_SERVER['REQUEST_METHOD'] != 'POST') {
            redirect('permissions');
        }

        // Validate CSRF token
        if(!Security::validateCsrfToken($_POST['csrf_token'])) {
            flash('permission_message', 'Security token validation failed', 'alert alert-danger');
            redirect('permissions');
        }

        $permission = $this->permissionModel->getPermissionById($id);

        if(!$permission) {
            flash('permission_message', 'Permission not found', 'alert alert-danger');
            redirect('permissions');
        }

        // Get roles that have this permission before deleting it
        $roles = $this->permissionModel->getRolesWithPermission($id);

        // Delete permission
        if($this->permissionModel->deletePermission($id)) {
            // Clear permission cache for the current user
            clearPermissionCache();

            // Update last_modified timestamp for all affected roles
            foreach($roles as $role) {
                $this->roleModel->updateRoleLastModified($role->id);
            }

            flash('permission_message', 'Permission deleted successfully', 'alert alert-success');
        } else {
            flash('permission_message', 'Something went wrong', 'alert alert-danger');
        }

        redirect('permissions');
    }
}
