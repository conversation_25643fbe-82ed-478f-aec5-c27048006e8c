\doxysection{Asset Class Reference}
\hypertarget{class_asset}{}\label{class_asset}\index{Asset@{Asset}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_asset_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_asset_a60da5dbc75bf17137f158e70b09ef9ae}{get\+Assets}} (\$page=1, \$per\+Page=10, \$sort\+Field=\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}, \$sort\+Order=\textquotesingle{}DESC\textquotesingle{})
\item 
\mbox{\hyperlink{class_asset_a69a1960e84310a44bf8dbdeda4e34fdb}{get\+Asset\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_asset_a91dfab2328e0161d3dca63dfcb8b097f}{add\+Asset}} (\$data)
\item 
\mbox{\hyperlink{class_asset_acdfee112e7e094af03bb569595542f2f}{update\+Asset}} (\$data)
\item 
\mbox{\hyperlink{class_asset_ae20ad8af6f2a4eda4e331bd8739f2dac}{delete\+Asset}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_asset_a9ed8debba2e7a08cce8fc517d61ef203}{search\+Assets}} (\$term, \$page=1, \$per\+Page=10, \$sort\+Field=\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}, \$sort\+Order=\textquotesingle{}DESC\textquotesingle{})
\item 
\mbox{\hyperlink{class_asset_a488f0cf663d59902ee8008a71964f62c}{advanced\+Search\+Assets}} (\$params, \$page=1, \$per\+Page=10)
\item 
\mbox{\hyperlink{class_asset_aaab70ee74a77249c0cb51a3ccd2eefd8}{get\+Unique\+Field\+Values}} (\$field)
\item 
\mbox{\hyperlink{class_asset_a6ab50787cfda440dabe402645ff8ac6d}{get\+Assets\+For\+Export}} (\$term=null)
\item 
\mbox{\hyperlink{class_asset_a7d272bd5a18c11c185325475ba86fe99}{get\+Asset\+History}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_asset_a4f1964df346c70fa2a3ef319bd9acb7a}{get\+Asset\+Count}} ()
\item 
\mbox{\hyperlink{class_asset_a42dc68dc181d645174c79dc64d32a78d}{import\+From\+CSV}} (\$file, \$skip\+Rows=7)
\item 
\mbox{\hyperlink{class_asset_a6425eeb4a115b8e037133d0bb3a8a82f}{get\+Total\+Assets\+Count}} ()
\item 
\mbox{\hyperlink{class_asset_adcb3b1e930889634a52b36d2652b2eca}{get\+Asset\+Count\+By\+Type}} ()
\item 
\mbox{\hyperlink{class_asset_ab6d82658428d5a3485a9c14c00c4cb91}{get\+Asset\+Count\+By\+Acquisition\+Date}} ()
\item 
\mbox{\hyperlink{class_asset_acce8c1627f559a128e886639b0954511}{get\+Asset\+Count\+By\+Employee}} ()
\item 
\mbox{\hyperlink{class_asset_adf321bfffefe63d041f00ad6adfca3fd}{get\+Asset\+Count\+By\+OS}} ()
\item 
\mbox{\hyperlink{class_asset_abdc448f418bdb34fd6cce81074ed02d8}{get\+Asset\+Count\+By\+Site}} ()
\item 
\mbox{\hyperlink{class_asset_a77a2c945fd9edfec16d03c352b983b58}{get\+Assets\+By\+Ids}} (\$ids)
\item 
\mbox{\hyperlink{class_asset_ab743e64ad3125a35028e7e8eb8c862fd}{serial\+Number\+Exists}} (\$serial\+Number)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_asset_a095c5d389db211932136b53f25f39685}\index{Asset@{Asset}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_asset_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_asset_a91dfab2328e0161d3dca63dfcb8b097f}\index{Asset@{Asset}!addAsset@{addAsset}}
\index{addAsset@{addAsset}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{addAsset()}{addAsset()}}
{\footnotesize\ttfamily \label{class_asset_a91dfab2328e0161d3dca63dfcb8b097f} 
add\+Asset (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a488f0cf663d59902ee8008a71964f62c}\index{Asset@{Asset}!advancedSearchAssets@{advancedSearchAssets}}
\index{advancedSearchAssets@{advancedSearchAssets}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{advancedSearchAssets()}{advancedSearchAssets()}}
{\footnotesize\ttfamily \label{class_asset_a488f0cf663d59902ee8008a71964f62c} 
advanced\+Search\+Assets (\begin{DoxyParamCaption}\item[{}]{\$params}{, }\item[{}]{\$page}{ = {\ttfamily 1}, }\item[{}]{\$per\+Page}{ = {\ttfamily 10}}\end{DoxyParamCaption})}

\Hypertarget{class_asset_ae20ad8af6f2a4eda4e331bd8739f2dac}\index{Asset@{Asset}!deleteAsset@{deleteAsset}}
\index{deleteAsset@{deleteAsset}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{deleteAsset()}{deleteAsset()}}
{\footnotesize\ttfamily \label{class_asset_ae20ad8af6f2a4eda4e331bd8739f2dac} 
delete\+Asset (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a69a1960e84310a44bf8dbdeda4e34fdb}\index{Asset@{Asset}!getAssetById@{getAssetById}}
\index{getAssetById@{getAssetById}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetById()}{getAssetById()}}
{\footnotesize\ttfamily \label{class_asset_a69a1960e84310a44bf8dbdeda4e34fdb} 
get\+Asset\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a4f1964df346c70fa2a3ef319bd9acb7a}\index{Asset@{Asset}!getAssetCount@{getAssetCount}}
\index{getAssetCount@{getAssetCount}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCount()}{getAssetCount()}}
{\footnotesize\ttfamily \label{class_asset_a4f1964df346c70fa2a3ef319bd9acb7a} 
get\+Asset\+Count (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_ab6d82658428d5a3485a9c14c00c4cb91}\index{Asset@{Asset}!getAssetCountByAcquisitionDate@{getAssetCountByAcquisitionDate}}
\index{getAssetCountByAcquisitionDate@{getAssetCountByAcquisitionDate}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCountByAcquisitionDate()}{getAssetCountByAcquisitionDate()}}
{\footnotesize\ttfamily \label{class_asset_ab6d82658428d5a3485a9c14c00c4cb91} 
get\+Asset\+Count\+By\+Acquisition\+Date (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_acce8c1627f559a128e886639b0954511}\index{Asset@{Asset}!getAssetCountByEmployee@{getAssetCountByEmployee}}
\index{getAssetCountByEmployee@{getAssetCountByEmployee}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCountByEmployee()}{getAssetCountByEmployee()}}
{\footnotesize\ttfamily \label{class_asset_acce8c1627f559a128e886639b0954511} 
get\+Asset\+Count\+By\+Employee (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_adf321bfffefe63d041f00ad6adfca3fd}\index{Asset@{Asset}!getAssetCountByOS@{getAssetCountByOS}}
\index{getAssetCountByOS@{getAssetCountByOS}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCountByOS()}{getAssetCountByOS()}}
{\footnotesize\ttfamily \label{class_asset_adf321bfffefe63d041f00ad6adfca3fd} 
get\+Asset\+Count\+By\+OS (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_abdc448f418bdb34fd6cce81074ed02d8}\index{Asset@{Asset}!getAssetCountBySite@{getAssetCountBySite}}
\index{getAssetCountBySite@{getAssetCountBySite}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCountBySite()}{getAssetCountBySite()}}
{\footnotesize\ttfamily \label{class_asset_abdc448f418bdb34fd6cce81074ed02d8} 
get\+Asset\+Count\+By\+Site (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_adcb3b1e930889634a52b36d2652b2eca}\index{Asset@{Asset}!getAssetCountByType@{getAssetCountByType}}
\index{getAssetCountByType@{getAssetCountByType}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetCountByType()}{getAssetCountByType()}}
{\footnotesize\ttfamily \label{class_asset_adcb3b1e930889634a52b36d2652b2eca} 
get\+Asset\+Count\+By\+Type (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a7d272bd5a18c11c185325475ba86fe99}\index{Asset@{Asset}!getAssetHistory@{getAssetHistory}}
\index{getAssetHistory@{getAssetHistory}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetHistory()}{getAssetHistory()}}
{\footnotesize\ttfamily \label{class_asset_a7d272bd5a18c11c185325475ba86fe99} 
get\+Asset\+History (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a60da5dbc75bf17137f158e70b09ef9ae}\index{Asset@{Asset}!getAssets@{getAssets}}
\index{getAssets@{getAssets}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssets()}{getAssets()}}
{\footnotesize\ttfamily \label{class_asset_a60da5dbc75bf17137f158e70b09ef9ae} 
get\+Assets (\begin{DoxyParamCaption}\item[{}]{\$page}{ = {\ttfamily 1}, }\item[{}]{\$per\+Page}{ = {\ttfamily 10}, }\item[{}]{\$sort\+Field}{ = {\ttfamily \textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}}, }\item[{}]{\$sort\+Order}{ = {\ttfamily \textquotesingle{}DESC\textquotesingle{}}}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a77a2c945fd9edfec16d03c352b983b58}\index{Asset@{Asset}!getAssetsByIds@{getAssetsByIds}}
\index{getAssetsByIds@{getAssetsByIds}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetsByIds()}{getAssetsByIds()}}
{\footnotesize\ttfamily \label{class_asset_a77a2c945fd9edfec16d03c352b983b58} 
get\+Assets\+By\+Ids (\begin{DoxyParamCaption}\item[{}]{\$ids}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a6ab50787cfda440dabe402645ff8ac6d}\index{Asset@{Asset}!getAssetsForExport@{getAssetsForExport}}
\index{getAssetsForExport@{getAssetsForExport}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getAssetsForExport()}{getAssetsForExport()}}
{\footnotesize\ttfamily \label{class_asset_a6ab50787cfda440dabe402645ff8ac6d} 
get\+Assets\+For\+Export (\begin{DoxyParamCaption}\item[{}]{\$term}{ = {\ttfamily null}}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a6425eeb4a115b8e037133d0bb3a8a82f}\index{Asset@{Asset}!getTotalAssetsCount@{getTotalAssetsCount}}
\index{getTotalAssetsCount@{getTotalAssetsCount}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getTotalAssetsCount()}{getTotalAssetsCount()}}
{\footnotesize\ttfamily \label{class_asset_a6425eeb4a115b8e037133d0bb3a8a82f} 
get\+Total\+Assets\+Count (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_aaab70ee74a77249c0cb51a3ccd2eefd8}\index{Asset@{Asset}!getUniqueFieldValues@{getUniqueFieldValues}}
\index{getUniqueFieldValues@{getUniqueFieldValues}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{getUniqueFieldValues()}{getUniqueFieldValues()}}
{\footnotesize\ttfamily \label{class_asset_aaab70ee74a77249c0cb51a3ccd2eefd8} 
get\+Unique\+Field\+Values (\begin{DoxyParamCaption}\item[{}]{\$field}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a42dc68dc181d645174c79dc64d32a78d}\index{Asset@{Asset}!importFromCSV@{importFromCSV}}
\index{importFromCSV@{importFromCSV}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{importFromCSV()}{importFromCSV()}}
{\footnotesize\ttfamily \label{class_asset_a42dc68dc181d645174c79dc64d32a78d} 
import\+From\+CSV (\begin{DoxyParamCaption}\item[{}]{\$file}{, }\item[{}]{\$skip\+Rows}{ = {\ttfamily 7}}\end{DoxyParamCaption})}

\Hypertarget{class_asset_a9ed8debba2e7a08cce8fc517d61ef203}\index{Asset@{Asset}!searchAssets@{searchAssets}}
\index{searchAssets@{searchAssets}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{searchAssets()}{searchAssets()}}
{\footnotesize\ttfamily \label{class_asset_a9ed8debba2e7a08cce8fc517d61ef203} 
search\+Assets (\begin{DoxyParamCaption}\item[{}]{\$term}{, }\item[{}]{\$page}{ = {\ttfamily 1}, }\item[{}]{\$per\+Page}{ = {\ttfamily 10}, }\item[{}]{\$sort\+Field}{ = {\ttfamily \textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}}, }\item[{}]{\$sort\+Order}{ = {\ttfamily \textquotesingle{}DESC\textquotesingle{}}}\end{DoxyParamCaption})}

\Hypertarget{class_asset_ab743e64ad3125a35028e7e8eb8c862fd}\index{Asset@{Asset}!serialNumberExists@{serialNumberExists}}
\index{serialNumberExists@{serialNumberExists}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{serialNumberExists()}{serialNumberExists()}}
{\footnotesize\ttfamily \label{class_asset_ab743e64ad3125a35028e7e8eb8c862fd} 
serial\+Number\+Exists (\begin{DoxyParamCaption}\item[{}]{\$serial\+Number}{}\end{DoxyParamCaption})}

Check if an asset with the given serial number already exists


\begin{DoxyParams}[1]{Parameters}
string & {\em \$serial\+Number} & The serial number to check \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the serial number exists, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_asset_acdfee112e7e094af03bb569595542f2f}\index{Asset@{Asset}!updateAsset@{updateAsset}}
\index{updateAsset@{updateAsset}!Asset@{Asset}}
\doxysubsubsection{\texorpdfstring{updateAsset()}{updateAsset()}}
{\footnotesize\ttfamily \label{class_asset_acdfee112e7e094af03bb569595542f2f} 
update\+Asset (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{models_2_asset_8php}{Asset.\+php}}\end{DoxyCompactItemize}
