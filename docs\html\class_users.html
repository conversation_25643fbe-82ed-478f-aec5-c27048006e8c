<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Users Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Users Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Users:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_users.png" usemap="#Users_map" alt=""/>
  <map id="Users_map" name="Users_map">
<area href="class_controller.html" alt="Controller" shape="rect" coords="0,0,68,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc294a6cc8e69743746820e3d15e3f78" id="r_acc294a6cc8e69743746820e3d15e3f78"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acc294a6cc8e69743746820e3d15e3f78">register</a> ()</td></tr>
<tr class="separator:acc294a6cc8e69743746820e3d15e3f78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa311da27ba5706f5710cea7706c8eae1" id="r_aa311da27ba5706f5710cea7706c8eae1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa311da27ba5706f5710cea7706c8eae1">login</a> ()</td></tr>
<tr class="separator:aa311da27ba5706f5710cea7706c8eae1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc10a3bb76cb1118a8c869e2e3ae03a" id="r_aefc10a3bb76cb1118a8c869e2e3ae03a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aefc10a3bb76cb1118a8c869e2e3ae03a">createUserSession</a> ($user, $rememberMe=false)</td></tr>
<tr class="separator:aefc10a3bb76cb1118a8c869e2e3ae03a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bd7264f00dc27e7316ac4382a1f0f4c" id="r_a7bd7264f00dc27e7316ac4382a1f0f4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7bd7264f00dc27e7316ac4382a1f0f4c">profile</a> ()</td></tr>
<tr class="separator:a7bd7264f00dc27e7316ac4382a1f0f4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a082405d89acd6835c3a7c7a08a7adbab" id="r_a082405d89acd6835c3a7c7a08a7adbab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a082405d89acd6835c3a7c7a08a7adbab">logout</a> ()</td></tr>
<tr class="separator:a082405d89acd6835c3a7c7a08a7adbab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad960fc122349e70360e1c0b72813f6f1" id="r_ad960fc122349e70360e1c0b72813f6f1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad960fc122349e70360e1c0b72813f6f1">manage</a> ()</td></tr>
<tr class="separator:ad960fc122349e70360e1c0b72813f6f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32f5d55b63247885ba0ad182f67fdc1c" id="r_a32f5d55b63247885ba0ad182f67fdc1c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a32f5d55b63247885ba0ad182f67fdc1c">toggleStatus</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a32f5d55b63247885ba0ad182f67fdc1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a380fe18e9e8d209d022ae5e1d6ba1af8" id="r_a380fe18e9e8d209d022ae5e1d6ba1af8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a380fe18e9e8d209d022ae5e1d6ba1af8">toggleRole</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a380fe18e9e8d209d022ae5e1d6ba1af8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73ca737a6f9f1af62cb8c7a4a53b589d" id="r_a73ca737a6f9f1af62cb8c7a4a53b589d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73ca737a6f9f1af62cb8c7a4a53b589d">roles</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a73ca737a6f9f1af62cb8c7a4a53b589d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeca207a42dbe064c5646029f139aa787" id="r_aeca207a42dbe064c5646029f139aa787"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeca207a42dbe064c5646029f139aa787">resetPassword</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:aeca207a42dbe064c5646029f139aa787"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a192e204d6fea186666df606760e5169f" id="r_a192e204d6fea186666df606760e5169f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a192e204d6fea186666df606760e5169f">forgotPassword</a> ()</td></tr>
<tr class="separator:a192e204d6fea186666df606760e5169f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd105defab1eca8a3fb91bca9ba910e4" id="r_abd105defab1eca8a3fb91bca9ba910e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abd105defab1eca8a3fb91bca9ba910e4">resetPasswordWithToken</a> ()</td></tr>
<tr class="separator:abd105defab1eca8a3fb91bca9ba910e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller" id="r_ac531eb761b130b1925a8bae5c33af2fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#ac531eb761b130b1925a8bae5c33af2fc">model</a> ($model)</td></tr>
<tr class="separator:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller" id="r_a11f0e20b30b899d00b009a9bb1afe43d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a11f0e20b30b899d00b009a9bb1afe43d">view</a> ($view, $data=[])</td></tr>
<tr class="separator:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pro_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller" id="r_a0d92de8136cebc006a407442aab9db0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a0d92de8136cebc006a407442aab9db0a">sanitizePostData</a> ($data)</td></tr>
<tr class="separator:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller" id="r_aaf7b7d5aa2f9ec7a1f79646322121f52"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#aaf7b7d5aa2f9ec7a1f79646322121f52">validateCsrfToken</a> ($token)</td></tr>
<tr class="separator:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aefc10a3bb76cb1118a8c869e2e3ae03a" name="aefc10a3bb76cb1118a8c869e2e3ae03a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc10a3bb76cb1118a8c869e2e3ae03a">&#9670;&#160;</a></span>createUserSession()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">createUserSession </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$user</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$rememberMe</em></span><span class="paramdefsep"> = </span><span class="paramdefval">false</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a192e204d6fea186666df606760e5169f" name="a192e204d6fea186666df606760e5169f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a192e204d6fea186666df606760e5169f">&#9670;&#160;</a></span>forgotPassword()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">forgotPassword </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa311da27ba5706f5710cea7706c8eae1" name="aa311da27ba5706f5710cea7706c8eae1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa311da27ba5706f5710cea7706c8eae1">&#9670;&#160;</a></span>login()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">login </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a082405d89acd6835c3a7c7a08a7adbab" name="a082405d89acd6835c3a7c7a08a7adbab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a082405d89acd6835c3a7c7a08a7adbab">&#9670;&#160;</a></span>logout()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">logout </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad960fc122349e70360e1c0b72813f6f1" name="ad960fc122349e70360e1c0b72813f6f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad960fc122349e70360e1c0b72813f6f1">&#9670;&#160;</a></span>manage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">manage </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7bd7264f00dc27e7316ac4382a1f0f4c" name="a7bd7264f00dc27e7316ac4382a1f0f4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7bd7264f00dc27e7316ac4382a1f0f4c">&#9670;&#160;</a></span>profile()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">profile </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acc294a6cc8e69743746820e3d15e3f78" name="acc294a6cc8e69743746820e3d15e3f78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acc294a6cc8e69743746820e3d15e3f78">&#9670;&#160;</a></span>register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">register </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aeca207a42dbe064c5646029f139aa787" name="aeca207a42dbe064c5646029f139aa787"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeca207a42dbe064c5646029f139aa787">&#9670;&#160;</a></span>resetPassword()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">resetPassword </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abd105defab1eca8a3fb91bca9ba910e4" name="abd105defab1eca8a3fb91bca9ba910e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd105defab1eca8a3fb91bca9ba910e4">&#9670;&#160;</a></span>resetPasswordWithToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">resetPasswordWithToken </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a73ca737a6f9f1af62cb8c7a4a53b589d" name="a73ca737a6f9f1af62cb8c7a4a53b589d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73ca737a6f9f1af62cb8c7a4a53b589d">&#9670;&#160;</a></span>roles()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">roles </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Manage roles for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a380fe18e9e8d209d022ae5e1d6ba1af8" name="a380fe18e9e8d209d022ae5e1d6ba1af8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a380fe18e9e8d209d022ae5e1d6ba1af8">&#9670;&#160;</a></span>toggleRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">toggleRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a32f5d55b63247885ba0ad182f67fdc1c" name="a32f5d55b63247885ba0ad182f67fdc1c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32f5d55b63247885ba0ad182f67fdc1c">&#9670;&#160;</a></span>toggleStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">toggleStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/controllers/<a class="el" href="_users_8php.html">Users.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
