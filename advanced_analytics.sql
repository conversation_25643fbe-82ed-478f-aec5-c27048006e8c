-- Predictive Maintenance Tables
-- Add maintenance_history table to track all maintenance activities
CREATE TABLE IF NOT EXISTS maintenance_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    maintenance_type ENUM('preventive', 'corrective', 'upgrade', 'inspection') NOT NULL,
    description TEXT NOT NULL,
    performed_by INT,
    performed_date DATE NOT NULL,
    cost DECIMAL(10,2),
    next_scheduled_date DATE,
    status ENUM('scheduled', 'completed', 'cancelled', 'overdue') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add asset_health_metrics table to store calculated health scores
CREATE TABLE IF NOT EXISTS asset_health_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    health_score DECIMAL(5,2) NOT NULL, -- 0-100 score
    estimated_remaining_life INT, -- in days
    failure_probability DECIMAL(5,2), -- 0-100 percentage
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
);

-- Add asset_failures table to track failures for predictive modeling
CREATE TABLE IF NOT EXISTS asset_failures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    failure_date DATE NOT NULL,
    failure_type VARCHAR(100) NOT NULL,
    description TEXT,
    resolution TEXT,
    downtime_hours DECIMAL(10,2),
    repair_cost DECIMAL(10,2),
    reported_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_maintenance_history_asset_id ON maintenance_history(asset_id);
CREATE INDEX idx_maintenance_history_performed_date ON maintenance_history(performed_date);
CREATE INDEX idx_asset_health_metrics_asset_id ON asset_health_metrics(asset_id);
CREATE INDEX idx_asset_failures_asset_id ON asset_failures(asset_id);
CREATE INDEX idx_asset_failures_failure_date ON asset_failures(failure_date);

-- Cost Analysis Tables
-- Add asset_costs table to track all costs associated with assets
CREATE TABLE IF NOT EXISTS asset_costs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    cost_type ENUM('acquisition', 'maintenance', 'upgrade', 'repair', 'license', 'other') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    date_incurred DATE NOT NULL,
    fiscal_year INT,
    budget_category VARCHAR(100),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add budget_forecasts table to store budget projections
CREATE TABLE IF NOT EXISTS budget_forecasts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fiscal_year INT NOT NULL,
    quarter TINYINT,
    category VARCHAR(100) NOT NULL,
    projected_amount DECIMAL(10,2) NOT NULL,
    actual_amount DECIMAL(10,2),
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add depreciation_settings table to configure depreciation calculations
CREATE TABLE IF NOT EXISTS depreciation_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_type VARCHAR(100) NOT NULL,
    useful_life_years INT NOT NULL,
    depreciation_method ENUM('straight_line', 'declining_balance', 'sum_of_years_digits') DEFAULT 'straight_line',
    salvage_value_percentage DECIMAL(5,2) DEFAULT 10.00,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (equipment_type),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_asset_costs_asset_id ON asset_costs(asset_id);
CREATE INDEX idx_asset_costs_date_incurred ON asset_costs(date_incurred);
CREATE INDEX idx_asset_costs_fiscal_year ON asset_costs(fiscal_year);
CREATE INDEX idx_budget_forecasts_fiscal_year ON budget_forecasts(fiscal_year);
CREATE INDEX idx_budget_forecasts_category ON budget_forecasts(category);

-- Compliance Reporting Tables
-- Add compliance_frameworks table to define compliance frameworks
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add compliance_controls table to define controls within frameworks
CREATE TABLE IF NOT EXISTS compliance_controls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    framework_id INT NOT NULL,
    control_id VARCHAR(50) NOT NULL, -- e.g., "AC-1", "CM-7"
    name VARCHAR(255) NOT NULL,
    description TEXT,
    implementation_guidance TEXT,
    verification_procedure TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add asset_compliance table to track compliance status for assets
CREATE TABLE IF NOT EXISTS asset_compliance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    control_id INT NOT NULL,
    status ENUM('compliant', 'non_compliant', 'not_applicable', 'in_progress') DEFAULT 'in_progress',
    evidence TEXT,
    notes TEXT,
    last_assessed_by INT,
    last_assessed_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (control_id) REFERENCES compliance_controls(id) ON DELETE CASCADE,
    FOREIGN KEY (last_assessed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add compliance_reports table to store generated reports
CREATE TABLE IF NOT EXISTS compliance_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    framework_id INT,
    report_name VARCHAR(255) NOT NULL,
    report_date DATE NOT NULL,
    report_data LONGTEXT, -- JSON data
    generated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id) ON DELETE SET NULL,
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_compliance_controls_framework_id ON compliance_controls(framework_id);
CREATE INDEX idx_asset_compliance_asset_id ON asset_compliance(asset_id);
CREATE INDEX idx_asset_compliance_control_id ON asset_compliance(control_id);
CREATE INDEX idx_asset_compliance_status ON asset_compliance(status);
CREATE INDEX idx_compliance_reports_framework_id ON compliance_reports(framework_id);

-- Insert sample compliance frameworks
INSERT INTO compliance_frameworks (name, description, version, active) VALUES
('NIST SP 800-53', 'Security and Privacy Controls for Federal Information Systems and Organizations', 'Rev. 5', TRUE),
('ISO 27001', 'Information Security Management System', '2013', TRUE),
('CIS Controls', 'Center for Internet Security Controls', 'v8', TRUE);

-- Insert sample controls for NIST SP 800-53
INSERT INTO compliance_controls (framework_id, control_id, name, description, implementation_guidance, verification_procedure, active) VALUES
(1, 'CM-8', 'Information System Component Inventory', 'Develop and document an inventory of information system components that accurately reflects the current system.', 'Maintain an up-to-date inventory of all hardware assets within the organization.', 'Verify that all assets are recorded in the inventory system with accurate information.', TRUE),
(1, 'CM-2', 'Baseline Configuration', 'Develop, document, and maintain a current baseline configuration of the information system.', 'Document standard configurations for all endpoint types.', 'Verify that assets adhere to the documented baseline configurations.', TRUE),
(1, 'SI-3', 'Malicious Code Protection', 'Implement malicious code protection mechanisms at information system entry and exit points.', 'Ensure all endpoints have antivirus/EDR solutions installed and updated.', 'Verify that all assets have the required security software installed and updated.', TRUE);
