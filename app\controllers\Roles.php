<?php
class Roles extends Controller {
    private $roleModel;
    private $permissionModel;
    private $userModel;

    public function __construct() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user has permission to manage roles
        if(!hasPermission('manage_roles')) {
            flash('role_message', 'You do not have permission to manage roles', 'alert alert-danger');
            redirect('dashboard');
        }

        $this->roleModel = $this->model('Role');
        $this->permissionModel = $this->model('Permission');
        $this->userModel = $this->model('User');
    }

    /**
     * Display all roles
     */
    public function index() {
        $roles = $this->roleModel->getAllRoles();

        $data = [
            'roles' => $roles
        ];

        $this->view('roles/index', $data);
    }

    /**
     * Show role details
     *
     * @param int $id Role ID
     */
    public function show($id) {
        $role = $this->roleModel->getRoleById($id);

        if(!$role) {
            flash('role_message', 'Role not found', 'alert alert-danger');
            redirect('roles');
        }

        $permissions = $this->roleModel->getRolePermissions($id);
        $users = $this->roleModel->getUsersWithRole($id);

        $data = [
            'role' => $role,
            'permissions' => $permissions,
            'users' => $users
        ];

        $this->view('roles/show', $data);
    }

    /**
     * Add a new role
     */
    public function add() {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('role_message', 'Security token validation failed', 'alert alert-danger');
                redirect('roles');
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'name_err' => '',
                'description_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter role name';
            } else {
                // Check if role already exists
                $existingRole = $this->roleModel->getRoleByName($data['name']);
                if($existingRole) {
                    $data['name_err'] = 'Role with this name already exists';
                }
            }

            // Validate description
            if(empty($data['description'])) {
                $data['description_err'] = 'Please enter role description';
            }

            // Make sure errors are empty
            if(empty($data['name_err']) && empty($data['description_err'])) {
                // Create role
                if($this->roleModel->createRole($data)) {
                    flash('role_message', 'Role added successfully', 'alert alert-success');
                    redirect('roles');
                } else {
                    flash('role_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('roles/add', $data);
                }
            } else {
                // Load view with errors
                $this->view('roles/add', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'name_err' => '',
                'description_err' => ''
            ];

            $this->view('roles/add', $data);
        }
    }

    /**
     * Edit a role
     *
     * @param int $id Role ID
     */
    public function edit($id) {
        $role = $this->roleModel->getRoleById($id);

        if(!$role) {
            flash('role_message', 'Role not found', 'alert alert-danger');
            redirect('roles');
        }

        // Check if it's a system role
        if($role->is_system_role) {
            flash('role_message', 'System roles cannot be edited', 'alert alert-danger');
            redirect('roles');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('role_message', 'Security token validation failed', 'alert alert-danger');
                redirect('roles');
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'name_err' => '',
                'description_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter role name';
            } else {
                // Check if role already exists (excluding current role)
                $existingRole = $this->roleModel->getRoleByName($data['name']);
                if($existingRole && $existingRole->id != $id) {
                    $data['name_err'] = 'Role with this name already exists';
                }
            }

            // Validate description
            if(empty($data['description'])) {
                $data['description_err'] = 'Please enter role description';
            }

            // Make sure errors are empty
            if(empty($data['name_err']) && empty($data['description_err'])) {
                // Update role
                if($this->roleModel->updateRole($data)) {
                    flash('role_message', 'Role updated successfully', 'alert alert-success');
                    redirect('roles');
                } else {
                    flash('role_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('roles/edit', $data);
                }
            } else {
                // Load view with errors
                $this->view('roles/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $id,
                'name' => $role->name,
                'description' => $role->description,
                'name_err' => '',
                'description_err' => ''
            ];

            $this->view('roles/edit', $data);
        }
    }

    /**
     * Delete a role
     *
     * @param int $id Role ID
     */
    public function delete($id) {
        // Check if it's a POST request
        if($_SERVER['REQUEST_METHOD'] != 'POST') {
            redirect('roles');
        }

        // Validate CSRF token
        if(!Security::validateCsrfToken($_POST['csrf_token'])) {
            flash('role_message', 'Security token validation failed', 'alert alert-danger');
            redirect('roles');
        }

        $role = $this->roleModel->getRoleById($id);

        if(!$role) {
            flash('role_message', 'Role not found', 'alert alert-danger');
            redirect('roles');
        }

        // Check if it's a system role
        if($role->is_system_role) {
            flash('role_message', 'System roles cannot be deleted', 'alert alert-danger');
            redirect('roles');
        }

        // Delete role
        if($this->roleModel->deleteRole($id)) {
            flash('role_message', 'Role deleted successfully', 'alert alert-success');
        } else {
            flash('role_message', 'Something went wrong', 'alert alert-danger');
        }

        redirect('roles');
    }

    /**
     * Manage permissions for a role
     *
     * @param int $id Role ID
     */
    public function permissions($id) {
        $role = $this->roleModel->getRoleById($id);

        if(!$role) {
            flash('role_message', 'Role not found', 'alert alert-danger');
            redirect('roles');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('role_message', 'Security token validation failed', 'alert alert-danger');
                redirect('roles/permissions/' . $id);
            }

            // Get selected permissions
            $permissionIds = isset($_POST['permissions']) ? $_POST['permissions'] : [];

            // Assign permissions to role
            if($this->roleModel->assignPermissions($id, $permissionIds)) {
                // Clear permission cache for the current user
                clearPermissionCache();

                // For other users with this role, we'll need to clear their cache when they log in next
                // Let's add a timestamp to the role_permissions table to track when permissions were last updated
                $this->roleModel->updateRoleLastModified($id);

                flash('role_message', 'Permissions updated successfully', 'alert alert-success');
            } else {
                flash('role_message', 'Something went wrong', 'alert alert-danger');
            }

            redirect('roles/permissions/' . $id);
        } else {
            $rolePermissions = $this->roleModel->getRolePermissions($id);
            $allPermissions = $this->permissionModel->getAllPermissions();
            $categories = $this->permissionModel->getAllCategories();

            // Create an array of permission IDs for easy checking
            $rolePermissionIds = [];
            foreach($rolePermissions as $permission) {
                $rolePermissionIds[] = $permission->id;
            }

            $data = [
                'role' => $role,
                'rolePermissions' => $rolePermissions,
                'rolePermissionIds' => $rolePermissionIds,
                'allPermissions' => $allPermissions,
                'categories' => $categories
            ];

            $this->view('roles/permissions', $data);
        }
    }
}
