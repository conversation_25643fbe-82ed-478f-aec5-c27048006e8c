<?php
/**
 * Email Helper
 * Contains functions for sending emails using PHPMailer
 */

// PHPMailer classes
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Send an email using <PERSON><PERSON><PERSON>ailer
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative body
 * @return bool True if email sent successfully, false otherwise
 */
function sendEmail($to, $subject, $body, $altBody = '') {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);

    try {
        // Server settings
        // Uncomment the following lines and update with your SMTP settings when ready for production
        // $mail->SMTPDebug = SMTP::DEBUG_OFF;                      // Enable verbose debug output
        // $mail->isSMTP();                                         // Send using SMTP
        // $mail->Host       = 'smtp.example.com';                  // SMTP server
        // $mail->SMTPAuth   = true;                                // Enable SMTP authentication
        // $mail->Username   = '<EMAIL>';                  // SMTP username
        // $mail->Password   = 'password';                          // SMTP password
        // $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;      // Enable TLS encryption
        // $mail->Port       = 587;                                 // TCP port to connect to

        // For development/testing, use the local mail server
        $mail->isMail();

        // Recipients
        $mail->setFrom('noreply@' . $_SERVER['HTTP_HOST'], SITENAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        $mail->AltBody = $altBody ?: strip_tags($body);

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Email could not be sent. Mailer Error: {$mail->ErrorInfo}");
        return false;
    }
}

/**
 * Send a password reset email
 * 
 * @param string $to Recipient email address
 * @param string $name Recipient name
 * @param string $token Password reset token
 * @return bool True if email sent successfully, false otherwise
 */
function sendPasswordResetEmail($to, $name, $token) {
    $resetUrl = URLROOT . '/users/resetPassword?token=' . $token . '&email=' . urlencode($to);
    
    $subject = SITENAME . ' - Password Reset Request';
    
    $body = '
    <html>
    <head>
        <title>Password Reset</title>
    </head>
    <body>
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2 style="color: #3b82f6;">Password Reset Request</h2>
            <p>Hello ' . htmlspecialchars($name) . ',</p>
            <p>We received a request to reset your password for your account at ' . SITENAME . '.</p>
            <p>Please click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="' . $resetUrl . '" style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
            </p>
            <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>Regards,<br>The ' . SITENAME . ' Team</p>
        </div>
    </body>
    </html>';
    
    $altBody = "Hello " . $name . ",\n\n" .
               "We received a request to reset your password for your account at " . SITENAME . ".\n\n" .
               "Please visit the following link to reset your password:\n" .
               $resetUrl . "\n\n" .
               "If you did not request a password reset, please ignore this email or contact support if you have concerns.\n\n" .
               "This link will expire in 1 hour for security reasons.\n\n" .
               "Regards,\nThe " . SITENAME . " Team";
    
    return sendEmail($to, $subject, $body, $altBody);
}
