-- Add import_rate_limits table to track import operations
CREATE TABLE IF NOT EXISTS import_rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent VARCHAR(255),
    import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_name VARCHAR(255),
    file_size INT,
    success BOOLEAN DEFAULT FALSE,
    INDEX (user_id),
    INDEX (ip_address),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
