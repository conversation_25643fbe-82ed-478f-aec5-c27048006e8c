<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Security Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Security Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a70b34e128c2e082e28e4b133bf532fbb" id="r_a70b34e128c2e082e28e4b133bf532fbb"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a70b34e128c2e082e28e4b133bf532fbb">generateCsrfToken</a> ()</td></tr>
<tr class="separator:a70b34e128c2e082e28e4b133bf532fbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2efdb8dd02e7a6b3404fcc2efc9373be" id="r_a2efdb8dd02e7a6b3404fcc2efc9373be"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2efdb8dd02e7a6b3404fcc2efc9373be">validateCsrfToken</a> ($token, $removeAfterValidation=true)</td></tr>
<tr class="separator:a2efdb8dd02e7a6b3404fcc2efc9373be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b86b53ed28d8dc7b1e9fe2a2d21cda8" id="r_a5b86b53ed28d8dc7b1e9fe2a2d21cda8"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5b86b53ed28d8dc7b1e9fe2a2d21cda8">escapeOutput</a> ($output)</td></tr>
<tr class="separator:a5b86b53ed28d8dc7b1e9fe2a2d21cda8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7a83d3a48c6f050ffa947f770c291aa" id="r_aa7a83d3a48c6f050ffa947f770c291aa"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa7a83d3a48c6f050ffa947f770c291aa">sanitizeInput</a> ($input)</td></tr>
<tr class="separator:aa7a83d3a48c6f050ffa947f770c291aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7474581f8ab54f56d7c285f873d4512" id="r_ab7474581f8ab54f56d7c285f873d4512"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab7474581f8ab54f56d7c285f873d4512">sanitizePostData</a> ($postData)</td></tr>
<tr class="separator:ab7474581f8ab54f56d7c285f873d4512"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a643f5130705b7cbb5981d0edf91b4505" id="r_a643f5130705b7cbb5981d0edf91b4505"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a643f5130705b7cbb5981d0edf91b4505">setSecurityHeaders</a> ()</td></tr>
<tr class="separator:a643f5130705b7cbb5981d0edf91b4505"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_security.html">Security</a> Helper Contains methods for CSRF protection, XSS prevention, and other security measures </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a5b86b53ed28d8dc7b1e9fe2a2d21cda8" name="a5b86b53ed28d8dc7b1e9fe2a2d21cda8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5b86b53ed28d8dc7b1e9fe2a2d21cda8">&#9670;&#160;</a></span>escapeOutput()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static escapeOutput </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Escape output to prevent XSS attacks</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$output</td><td>The string to escape </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>string The escaped string </dd></dl>

</div>
</div>
<a id="a70b34e128c2e082e28e4b133bf532fbb" name="a70b34e128c2e082e28e4b133bf532fbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a70b34e128c2e082e28e4b133bf532fbb">&#9670;&#160;</a></span>generateCsrfToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static generateCsrfToken </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Generate a CSRF token and store it in the session</p>
<dl class="section return"><dt>Returns</dt><dd>string The generated CSRF token </dd></dl>

</div>
</div>
<a id="aa7a83d3a48c6f050ffa947f770c291aa" name="aa7a83d3a48c6f050ffa947f770c291aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7a83d3a48c6f050ffa947f770c291aa">&#9670;&#160;</a></span>sanitizeInput()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static sanitizeInput </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$input</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sanitize input to prevent XSS attacks</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string&#160;|&#160;array</td><td class="paramname">$input</td><td>The input to sanitize </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>string|array The sanitized input </dd></dl>

</div>
</div>
<a id="ab7474581f8ab54f56d7c285f873d4512" name="ab7474581f8ab54f56d7c285f873d4512"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7474581f8ab54f56d7c285f873d4512">&#9670;&#160;</a></span>sanitizePostData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static sanitizePostData </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$postData</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sanitize POST data (replacement for deprecated FILTER_SANITIZE_STRING)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$postData</td><td>The POST data to sanitize </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array The sanitized POST data </dd></dl>

</div>
</div>
<a id="a643f5130705b7cbb5981d0edf91b4505" name="a643f5130705b7cbb5981d0edf91b4505"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a643f5130705b7cbb5981d0edf91b4505">&#9670;&#160;</a></span>setSecurityHeaders()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static setSecurityHeaders </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set security headers for the response </p>

</div>
</div>
<a id="a2efdb8dd02e7a6b3404fcc2efc9373be" name="a2efdb8dd02e7a6b3404fcc2efc9373be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2efdb8dd02e7a6b3404fcc2efc9373be">&#9670;&#160;</a></span>validateCsrfToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static validateCsrfToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$token</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$removeAfterValidation</em></span><span class="paramdefsep"> = </span><span class="paramdefval">true</span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Validate a CSRF token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$token</td><td>The token to validate </td></tr>
    <tr><td class="paramtype">bool</td><td class="paramname">$removeAfterValidation</td><td>Whether to remove the token after validation </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the token is valid, false otherwise </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/helpers/<a class="el" href="_security_8php.html">Security.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
