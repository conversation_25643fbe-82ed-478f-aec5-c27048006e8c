-- Add maintenance_guidelines table to define maintenance types and requirements
CREATE TABLE IF NOT EXISTS maintenance_guidelines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    equipment_type VARCHAR(100) NOT NULL, -- Matches equipment_type in assets table
    frequency_days INT NOT NULL, -- Recommended frequency in days
    importance ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add maintenance_checklist table to define steps for each maintenance type
CREATE TABLE IF NOT EXISTS maintenance_checklist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    guideline_id INT NOT NULL,
    step_number INT NOT NULL,
    description TEXT NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (guideline_id) REFERENCES maintenance_guidelines(id) ON DELETE CASCADE
);

-- Add maintenance_compliance table to track which guidelines have been implemented for each asset
CREATE TABLE IF NOT EXISTS maintenance_compliance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    guideline_id INT NOT NULL,
    last_performed_date DATE,
    next_due_date DATE,
    compliance_status ENUM('compliant', 'due_soon', 'overdue', 'not_applicable') DEFAULT 'not_applicable',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    FOREIGN KEY (guideline_id) REFERENCES maintenance_guidelines(id) ON DELETE CASCADE,
    UNIQUE KEY (asset_id, guideline_id)
);

-- Add maintenance_checklist_completion table to track which checklist items were completed
CREATE TABLE IF NOT EXISTS maintenance_checklist_completion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    maintenance_history_id INT NOT NULL,
    checklist_id INT NOT NULL,
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    completed_by INT,
    completed_date DATETIME,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maintenance_history_id) REFERENCES maintenance_history(id) ON DELETE CASCADE,
    FOREIGN KEY (checklist_id) REFERENCES maintenance_checklist(id) ON DELETE CASCADE,
    FOREIGN KEY (completed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX idx_maintenance_guidelines_equipment_type ON maintenance_guidelines(equipment_type);
CREATE INDEX idx_maintenance_checklist_guideline_id ON maintenance_checklist(guideline_id);
CREATE INDEX idx_maintenance_compliance_asset_id ON maintenance_compliance(asset_id);
CREATE INDEX idx_maintenance_compliance_guideline_id ON maintenance_compliance(guideline_id);
CREATE INDEX idx_maintenance_compliance_next_due_date ON maintenance_compliance(next_due_date);
CREATE INDEX idx_maintenance_checklist_completion_maintenance_history_id ON maintenance_checklist_completion(maintenance_history_id);

-- Insert sample maintenance guidelines
INSERT INTO maintenance_guidelines (name, description, equipment_type, frequency_days, importance) VALUES
('Preventive Maintenance - Laptop', 'Regular preventive maintenance for laptops to ensure optimal performance and longevity.', 'Laptop', 90, 'medium'),
('Preventive Maintenance - Desktop', 'Regular preventive maintenance for desktop computers to ensure optimal performance and longevity.', 'Desktop', 90, 'medium'),
('Preventive Maintenance - Server', 'Regular preventive maintenance for servers to ensure optimal performance, reliability, and security.', 'Server', 30, 'high'),
('Software Update - Workstation', 'Regular software updates for workstations to ensure security and performance.', 'Laptop', 30, 'high'),
('Software Update - Workstation', 'Regular software updates for workstations to ensure security and performance.', 'Desktop', 30, 'high'),
('Software Update - Server', 'Regular software updates for servers to ensure security, stability, and performance.', 'Server', 14, 'critical'),
('Hardware Inspection - Server', 'Physical inspection of server hardware to identify potential issues before they cause failures.', 'Server', 60, 'high'),
('Security Audit - Workstation', 'Security audit for workstations to ensure compliance with security policies.', 'Laptop', 180, 'medium'),
('Security Audit - Workstation', 'Security audit for workstations to ensure compliance with security policies.', 'Desktop', 180, 'medium'),
('Security Audit - Server', 'Comprehensive security audit for servers to ensure compliance with security policies and best practices.', 'Server', 90, 'critical');

-- Insert sample checklist items for laptop preventive maintenance
INSERT INTO maintenance_checklist (guideline_id, step_number, description, is_required) VALUES
(1, 1, 'Clean exterior surfaces with appropriate cleaning solution', TRUE),
(1, 2, 'Clean keyboard and touchpad', TRUE),
(1, 3, 'Check battery health and replace if below 70% capacity', TRUE),
(1, 4, 'Clean cooling vents and fans', TRUE),
(1, 5, 'Check for and install system updates', TRUE),
(1, 6, 'Run disk cleanup and defragmentation', TRUE),
(1, 7, 'Check and update antivirus/security software', TRUE),
(1, 8, 'Verify backup system is working properly', TRUE),
(1, 9, 'Test all ports and connections', FALSE),
(1, 10, 'Check screen for dead pixels or other issues', FALSE);

-- Insert sample checklist items for desktop preventive maintenance
INSERT INTO maintenance_checklist (guideline_id, step_number, description, is_required) VALUES
(2, 1, 'Clean exterior surfaces with appropriate cleaning solution', TRUE),
(2, 2, 'Clean keyboard and mouse', TRUE),
(2, 3, 'Open case and clean interior dust with compressed air', TRUE),
(2, 4, 'Check and clean cooling fans', TRUE),
(2, 5, 'Check all internal connections', TRUE),
(2, 6, 'Check for and install system updates', TRUE),
(2, 7, 'Run disk cleanup and defragmentation', TRUE),
(2, 8, 'Check and update antivirus/security software', TRUE),
(2, 9, 'Verify backup system is working properly', TRUE),
(2, 10, 'Test all ports and connections', FALSE);

-- Insert sample checklist items for server preventive maintenance
INSERT INTO maintenance_checklist (guideline_id, step_number, description, is_required) VALUES
(3, 1, 'Check server logs for errors or warnings', TRUE),
(3, 2, 'Monitor system performance metrics', TRUE),
(3, 3, 'Check disk space and storage health', TRUE),
(3, 4, 'Verify RAID configuration is healthy', TRUE),
(3, 5, 'Check backup system is functioning properly', TRUE),
(3, 6, 'Verify UPS/power protection is functioning', TRUE),
(3, 7, 'Check cooling system performance', TRUE),
(3, 8, 'Verify network connectivity and throughput', TRUE),
(3, 9, 'Check for and apply critical security patches', TRUE),
(3, 10, 'Update documentation if any changes made', TRUE);
