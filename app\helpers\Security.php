<?php
/**
 * Security Helper
 * Contains methods for CSRF protection, XSS prevention, and other security measures
 */
class Security {
    /**
     * Generate a CSRF token and store it in the session
     *
     * @return string The generated CSRF token
     */
    public static function generateCsrfToken() {
        // Debug log
        error_log("Security::generateCsrfToken called");
        error_log("Session ID: " . session_id());

        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
            error_log("Initialized empty csrf_tokens array in session");
        }

        // Generate a random token
        $token = bin2hex(random_bytes(32));
        error_log("Generated new token: " . substr($token, 0, 10) . "...");

        // Store the token in the session with a timestamp
        $_SESSION['csrf_tokens'][$token] = time();
        error_log("Stored token in session. Total tokens: " . count($_SESSION['csrf_tokens']));

        // Clean up old tokens (tokens older than 2 hours)
        self::cleanupCsrfTokens();

        return $token;
    }

    /**
     * Validate a CSRF token
     *
     * @param string $token The token to validate
     * @param bool $removeAfterValidation Whether to remove the token after validation
     * @return bool True if the token is valid, false otherwise
     */
    public static function validateCsrfToken($token, $removeAfterValidation = true) {
        // Debug log
        error_log("Security::validateCsrfToken called with token: " . substr($token, 0, 10) . "...");
        error_log("Session ID: " . session_id());
        error_log("csrf_tokens exists in session: " . (isset($_SESSION['csrf_tokens']) ? 'Yes' : 'No'));

        if (!isset($_SESSION['csrf_tokens'])) {
            error_log("CSRF validation failed: csrf_tokens not set in session");
            return false;
        }

        if (!isset($_SESSION['csrf_tokens'][$token])) {
            error_log("CSRF validation failed: token not found in csrf_tokens");
            error_log("Available tokens: " . implode(', ', array_keys($_SESSION['csrf_tokens'])));
            return false;
        }

        // Token exists, check if it's not expired (2 hours)
        $tokenTime = $_SESSION['csrf_tokens'][$token];
        $isValid = (time() - $tokenTime) <= 7200; // 2 hours in seconds

        if (!$isValid) {
            error_log("CSRF validation failed: token expired");
        } else {
            error_log("CSRF validation passed");
        }

        // Remove the token if requested and it's valid
        if ($removeAfterValidation && $isValid) {
            unset($_SESSION['csrf_tokens'][$token]);
            error_log("Token removed from session after successful validation");
        }

        return $isValid;
    }

    /**
     * Clean up old CSRF tokens (older than 2 hours)
     */
    private static function cleanupCsrfTokens() {
        if (!isset($_SESSION['csrf_tokens'])) {
            return;
        }

        $now = time();
        foreach ($_SESSION['csrf_tokens'] as $token => $time) {
            if (($now - $time) > 7200) { // 2 hours in seconds
                unset($_SESSION['csrf_tokens'][$token]);
            }
        }
    }

    /**
     * Escape output to prevent XSS attacks
     *
     * @param string $output The string to escape
     * @return string The escaped string
     */
    public static function escapeOutput($output) {
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Sanitize input to prevent XSS attacks
     *
     * @param string|array $input The input to sanitize
     * @return string|array The sanitized input
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = self::sanitizeInput($value);
            }
            return $input;
        }

        // Remove potentially dangerous characters
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');

        return $input;
    }

    /**
     * Sanitize POST data (replacement for deprecated FILTER_SANITIZE_STRING)
     *
     * @param array $postData The POST data to sanitize
     * @return array The sanitized POST data
     */
    public static function sanitizePostData($postData) {
        if (!is_array($postData)) {
            return self::sanitizeInput($postData);
        }

        $sanitized = [];
        foreach ($postData as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = self::sanitizePostData($value);
            } else {
                // Trim and sanitize the value
                $sanitized[$key] = self::sanitizeInput(trim($value));
            }
        }

        return $sanitized;
    }

    /**
     * Set security headers for the response
     */
    public static function setSecurityHeaders() {
        // Temporarily disable Content Security Policy to debug UI issues
        // header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; style-src 'self' 'unsafe-inline' https: http:; font-src 'self' https: http: data:; img-src 'self' https: http: data:; connect-src 'self' https: http:;");

        // X-XSS-Protection
        header("X-XSS-Protection: 1; mode=block");

        // X-Content-Type-Options
        header("X-Content-Type-Options: nosniff");

        // X-Frame-Options
        header("X-Frame-Options: SAMEORIGIN");

        // Referrer-Policy
        header("Referrer-Policy: strict-origin-when-cross-origin");

        // Strict-Transport-Security (if using HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header("Strict-Transport-Security: max-age=31536000; includeSubDomains");
        }
    }
}
