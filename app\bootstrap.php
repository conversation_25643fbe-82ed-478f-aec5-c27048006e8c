<?php
// Load Config
require_once 'config/config.php';

// Define environment
define('ENVIRONMENT', 'development'); // Change to 'production' for production environment

// Load Helpers
require_once 'helpers/session_helper.php';
require_once 'helpers/Security.php';
require_once 'helpers/SecurityEnhancements.php';
require_once 'helpers/output_helper.php';
require_once 'helpers/email_helper.php';

// Set security headers
Security::setSecurityHeaders();

// Autoload Core Libraries and Models
spl_autoload_register(function($className) {
    // Define the possible paths for class files
    $paths = [
        APPROOT . '/core/' . $className . '.php',
        APPROOT . '/models/' . $className . '.php'
    ];

    // Check each path and require the file if it exists
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Create logs directory if it doesn't exist
$logsDir = APPROOT . '/logs';
if (!file_exists($logsDir)) {
    mkdir($logsDir, 0755, true);
}

// Load custom error handler (must be loaded after autoloader)
require_once 'helpers/error_handler.php';
