<?php
/**
 * Role Model
 * Handles database operations for roles
 */
class Role {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all roles
     *
     * @return array Array of role objects
     */
    public function getAllRoles() {
        $this->db->query('SELECT * FROM roles ORDER BY name ASC');
        return $this->db->resultSet();
    }

    /**
     * Get role by ID
     *
     * @param int $id Role ID
     * @return object Role object
     */
    public function getRoleById($id) {
        $this->db->query('SELECT * FROM roles WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get role by name
     *
     * @param string $name Role name
     * @return object Role object
     */
    public function getRoleByName($name) {
        $this->db->query('SELECT * FROM roles WHERE name = :name');
        $this->db->bind(':name', $name);
        return $this->db->single();
    }

    /**
     * Create a new role
     *
     * @param array $data Role data
     * @return bool True if successful, false otherwise
     */
    public function createRole($data) {
        $this->db->query('INSERT INTO roles (name, description, is_system_role) VALUES (:name, :description, :is_system_role)');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':is_system_role', isset($data['is_system_role']) ? $data['is_system_role'] : false);

        return $this->db->execute();
    }

    /**
     * Update a role
     *
     * @param array $data Role data
     * @return bool True if successful, false otherwise
     */
    public function updateRole($data) {
        $this->db->query('UPDATE roles SET name = :name, description = :description WHERE id = :id');
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);

        return $this->db->execute();
    }

    /**
     * Delete a role
     *
     * @param int $id Role ID
     * @return bool True if successful, false otherwise
     */
    public function deleteRole($id) {
        // Check if it's a system role
        $role = $this->getRoleById($id);
        if ($role && $role->is_system_role) {
            return false; // Cannot delete system roles
        }

        $this->db->query('DELETE FROM roles WHERE id = :id AND is_system_role = 0');
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }

    /**
     * Get all permissions for a role
     *
     * @param int $roleId Role ID
     * @return array Array of permission objects
     */
    public function getRolePermissions($roleId) {
        $this->db->query('SELECT p.* FROM permissions p
                         JOIN role_permissions rp ON p.id = rp.permission_id
                         WHERE rp.role_id = :role_id
                         ORDER BY p.category, p.name');
        $this->db->bind(':role_id', $roleId);

        return $this->db->resultSet();
    }

    /**
     * Assign permissions to a role
     *
     * @param int $roleId Role ID
     * @param array $permissionIds Array of permission IDs
     * @return bool True if successful, false otherwise
     */
    public function assignPermissions($roleId, $permissionIds) {
        // First, remove all existing permissions for this role
        $this->db->query('DELETE FROM role_permissions WHERE role_id = :role_id');
        $this->db->bind(':role_id', $roleId);
        $this->db->execute();

        // Then, add the new permissions
        $success = true;
        foreach ($permissionIds as $permissionId) {
            $this->db->query('INSERT INTO role_permissions (role_id, permission_id) VALUES (:role_id, :permission_id)');
            $this->db->bind(':role_id', $roleId);
            $this->db->bind(':permission_id', $permissionId);

            if (!$this->db->execute()) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Check if a role has a specific permission
     *
     * @param int $roleId Role ID
     * @param int $permissionId Permission ID
     * @return bool True if the role has the permission, false otherwise
     */
    public function hasPermission($roleId, $permissionId) {
        $this->db->query('SELECT COUNT(*) as count FROM role_permissions
                         WHERE role_id = :role_id AND permission_id = :permission_id');
        $this->db->bind(':role_id', $roleId);
        $this->db->bind(':permission_id', $permissionId);

        $result = $this->db->single();
        return $result->count > 0;
    }

    /**
     * Get all users with a specific role
     *
     * @param int $roleId Role ID
     * @return array Array of user objects
     */
    public function getUsersWithRole($roleId) {
        $this->db->query('SELECT u.* FROM users u
                         JOIN user_roles ur ON u.id = ur.user_id
                         WHERE ur.role_id = :role_id
                         ORDER BY u.name ASC');
        $this->db->bind(':role_id', $roleId);

        return $this->db->resultSet();
    }

    /**
     * Update the last_modified timestamp for a role
     * This is used to track when permissions for a role were last updated
     *
     * @param int $roleId Role ID
     * @return bool True if successful, false otherwise
     */
    public function updateRoleLastModified($roleId) {
        $this->db->query('UPDATE roles SET updated_at = CURRENT_TIMESTAMP WHERE id = :id');
        $this->db->bind(':id', $roleId);

        return $this->db->execute();
    }
}
